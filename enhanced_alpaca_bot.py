import asyncio
import json
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict, deque
import websocket
import threading
import time
import requests
from typing import List, Dict, Optional, Tuple
import logging
import os
from dotenv import load_dotenv
import numpy as np
from dataclasses import dataclass, field
from enum import Enum

# Windows-compatible imports for emergency close
try:
    import msvcrt  # Windows
    WINDOWS = True
except ImportError:
    WINDOWS = False

load_dotenv()

API_KEY = os.getenv('APCA_API_KEY_ID')
SECRET_KEY = os.getenv('APCA_API_SECRET_KEY')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

@dataclass
class TradeExecution:
    symbol: str
    side: str
    qty: int
    order_time: datetime
    fill_time: Optional[datetime] = None
    expected_price: float = 0.0
    actual_price: float = 0.0
    slippage: float = 0.0
    execution_time_ms: float = 0.0
    order_id: str = ""
    status: str = "submitted"

@dataclass
class Position:
    symbol: str
    qty: int
    side: str
    avg_price: float
    market_value: float
    unrealized_pl: float
    entry_time: datetime
    
@dataclass
class ClosedPosition:
    symbol: str
    exit_price: float
    exit_time: datetime
    profit_cents: float
    monitoring_active: bool = True
    price_history_after_close: List[float] = field(default_factory=list)
    
@dataclass
class PerformanceMetrics:
    total_trades: int = 0
    successful_trades: int = 0
    avg_execution_time_ms: float = 0.0
    avg_slippage: float = 0.0
    total_slippage: float = 0.0
    max_execution_time_ms: float = 0.0
    min_execution_time_ms: float = float('inf')
    executions: List[TradeExecution] = field(default_factory=list)

class EnhancedAlpacaBot:
    def __init__(self, api_key: str, secret_key: str, symbols: List[str], 
                 use_paper: bool = True, profit_target_cents: float = 15.0,
                 initial_qty: int = 25, max_averaging_levels: int = 5,
                 averaging_down_threshold: float = -0.02,
                 enable_reentry: bool = True,
                 reentry_price_drop_pct: float = -0.01,  # 1% drop for re-entry
                 reentry_monitoring_time: int = 300):  # 5 minutes (300 seconds)
        self.api_key = api_key
        self.secret_key = secret_key
        self.symbols = symbols if isinstance(symbols, list) else [symbols]
        self.use_paper = use_paper
        self.profit_target_cents = profit_target_cents
        
        # Averaging down parameters
        self.initial_qty = initial_qty
        self.max_averaging_levels = max_averaging_levels
        self.averaging_down_threshold = averaging_down_threshold
        self.current_averaging_level = 0
        
        # Peak price tracking parameters
        self.peak_tracking_window = 60  # Track peak over last 60 prices
        self.min_peak_profit = 0.05  # Minimum 0.05% profit to consider selling at peak
        self.recent_peaks = {}  # Symbol -> recent peak price
        self.peak_confirmation_counts = {}  # Symbol -> confirmation count
        self.required_peak_confirmations = 3  # Number of confirmations needed to confirm a peak
        
        # Re-entry strategy parameters
        self.enable_reentry = enable_reentry
        self.reentry_price_drop_pct = reentry_price_drop_pct
        self.reentry_monitoring_time = reentry_monitoring_time
        self.closed_positions = {}  # Symbol -> ClosedPosition
        self.reentry_timers = {}  # Symbol -> Timer
        
        # API endpoints
        self.api_base = "https://paper-api.alpaca.markets/v2" if use_paper else "https://api.alpaca.markets/v2"
        self.headers = {
            'APCA-API-KEY-ID': self.api_key,
            'APCA-API-SECRET-KEY': self.secret_key,
            'Content-Type': 'application/json'
        }
        
        # Trading state
        self.positions = {}
        self.pending_orders = {}
        self.performance = PerformanceMetrics()
        self.current_prices = {}
        self.account_info = None
        self.price_history = defaultdict(list)
        
        # WebSocket for real-time data
        self.ws = None
        self.is_connected = False
        self.authenticated = False
        
        # WebSocket URL
        self.ws_url = "wss://stream.data.alpaca.markets/v2/iex"  # Using IEX feed
        
        # Position sync timer
        self.position_sync_timer = None
        self.position_sync_interval = 30  # seconds
        
        # Emergency close features
        self.emergency_close_enabled = True
        self.keyboard_listener_active = False
        self.emergency_close_in_progress = False
        
    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            response = requests.get(f'{self.api_base}/account', headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                self.account_info = response.json()
                
                print("=" * 60)
                print("📋 ALPACA ACCOUNT INFORMATION")
                print("=" * 60)
                print(f"Account Number: {self.account_info.get('account_number', 'N/A')}")
                print(f"Status: {self.account_info.get('status', 'N/A')}")
                print(f"Currency: {self.account_info.get('currency', 'N/A')}")
                print(f"Buying Power: ${float(self.account_info.get('buying_power', 0)):,.2f}")
                print(f"Cash: ${float(self.account_info.get('cash', 0)):,.2f}")
                print(f"Portfolio Value: ${float(self.account_info.get('portfolio_value', 0)):,.2f}")
                print(f"Day Trading Buying Power: ${float(self.account_info.get('daytrading_buying_power', 0)):,.2f}")
                print(f"Day Trade Count: {self.account_info.get('daytrade_count', 0)}")
                print(f"Pattern Day Trader: {self.account_info.get('pattern_day_trader', False)}")
                print(f"Trading Blocked: {self.account_info.get('trading_blocked', False)}")
                print(f"Account Blocked: {self.account_info.get('account_blocked', False)}")
                print("=" * 60)
                
                return self.account_info
            else:
                logger.error(f"Failed to get account info: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_positions(self) -> Dict[str, Position]:
        """Get current positions"""
        try:
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                positions_data = response.json()
                self.positions = {}
                
                for pos_data in positions_data:
                    symbol = pos_data['symbol']
                    
                    # Handle different field names for average cost
                    avg_cost = 0.0
                    if 'avg_entry_price' in pos_data:
                        avg_cost = float(pos_data['avg_entry_price'])
                    elif 'avg_price' in pos_data:
                        avg_cost = float(pos_data['avg_price'])
                    elif 'avg_cost' in pos_data:
                        avg_cost = float(pos_data['avg_cost'])
                    else:
                        logger.warning(f"Could not find average cost field for {symbol}. Available fields: {list(pos_data.keys())}")
                        # Use current price as fallback
                        avg_cost = self.current_prices.get(symbol, 0.0)
                    
                    position = Position(
                        symbol=symbol,
                        qty=int(pos_data['qty']),
                        side=pos_data['side'],
                        avg_price=avg_cost,
                        market_value=float(pos_data.get('market_value', 0.0)),
                        unrealized_pl=float(pos_data.get('unrealized_pl', 0.0)),
                        entry_time=datetime.now()  # Alpaca doesn't provide entry time in positions
                    )
                    self.positions[symbol] = position
                
                if self.positions:
                    print("\n📊 CURRENT POSITIONS:")
                    print("-" * 60)
                    for symbol, pos in self.positions.items():
                        print(f"{symbol}: {pos.qty} shares @ ${pos.avg_price:.2f} | "
                              f"P&L: ${pos.unrealized_pl:.2f} | Value: ${pos.market_value:.2f}")
                    print("-" * 60)
                else:
                    print("\n📊 No open positions")
                
                return self.positions
            else:
                logger.error(f"Failed to get positions: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {}
    
    def place_market_order(self, symbol: str, qty: int, side: OrderSide) -> Optional[TradeExecution]:
        """Place a market order and track execution"""
        try:
            order_time = datetime.now()
            expected_price = self.current_prices.get(symbol, 0.0)
            
            order_data = {
                "symbol": symbol,
                "qty": qty,
                "side": side.value,
                "type": OrderType.MARKET.value,
                "time_in_force": "day"
            }
            
            print(f"🔄 Placing {side.value.upper()} order: {qty} shares of {symbol} at market price")
            
            response = requests.post(
                f'{self.api_base}/orders',
                headers=self.headers,
                json=order_data,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                order_response = response.json()
                order_id = order_response['id']
                
                print(f"✅ Order placed successfully - ID: {order_id}")
                
                # Create execution tracking object
                execution = TradeExecution(
                    symbol=symbol,
                    side=side.value,
                    qty=qty,
                    order_time=order_time,
                    expected_price=expected_price,
                    order_id=order_id
                )
                
                # Monitor order execution
                self.monitor_order_execution(execution)
                
                return execution
            else:
                logger.error(f"Failed to place order: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
    
    def monitor_order_execution(self, execution: TradeExecution):
        """Monitor order execution and calculate metrics"""
        max_wait_time = 30  # seconds
        check_interval = 0.5  # seconds
        waited = 0
        
        while waited < max_wait_time:
            try:
                response = requests.get(
                    f'{self.api_base}/orders/{execution.order_id}',
                    headers=self.headers,
                    timeout=5
                )
                
                if response.status_code == 200:
                    order_data = response.json()
                    status = order_data['status']
                    
                    if status == 'filled':
                        execution.fill_time = datetime.now()
                        execution.actual_price = float(order_data['filled_avg_price'])
                        execution.execution_time_ms = (execution.fill_time - execution.order_time).total_seconds() * 1000
                        execution.status = 'filled'
                        
                        # Calculate slippage
                        if execution.side == 'buy':
                            execution.slippage = execution.actual_price - execution.expected_price
                        else:  # sell
                            execution.slippage = execution.expected_price - execution.actual_price
                        
                        print(f"✅ Order FILLED: {execution.symbol} @ ${execution.actual_price:.2f}")
                        print(f"   Execution Time: {execution.execution_time_ms:.1f}ms")
                        print(f"   Slippage: ${execution.slippage:.4f}")
                        
                        # Update performance metrics
                        self.update_performance_metrics(execution)
                        
                        return execution
                    
                    elif status in ['canceled', 'rejected', 'expired']:
                        execution.status = status
                        print(f"❌ Order {status.upper()}: {execution.symbol}")
                        return execution
                
                time.sleep(check_interval)
                waited += check_interval
                
            except Exception as e:
                logger.error(f"Error monitoring order: {e}")
                break
        
        print(f"⏰ Order monitoring timeout for {execution.symbol}")
        execution.status = 'timeout'
        return execution
    
    def update_performance_metrics(self, execution: TradeExecution):
        """Update overall performance metrics"""
        self.performance.executions.append(execution)
        self.performance.total_trades += 1
        
        if execution.status == 'filled':
            self.performance.successful_trades += 1
            self.performance.total_slippage += abs(execution.slippage)
            
            # Update execution time metrics
            exec_time = execution.execution_time_ms
            self.performance.max_execution_time_ms = max(self.performance.max_execution_time_ms, exec_time)
            self.performance.min_execution_time_ms = min(self.performance.min_execution_time_ms, exec_time)
            
            # Calculate averages
            filled_executions = [e for e in self.performance.executions if e.status == 'filled']
            if filled_executions:
                self.performance.avg_execution_time_ms = np.mean([e.execution_time_ms for e in filled_executions])
                self.performance.avg_slippage = np.mean([abs(e.slippage) for e in filled_executions])

    def should_average_down(self, symbol: str) -> bool:
        """Determine if we should average down on a position"""
        # Check if we have a position for this symbol
        if symbol not in self.positions:
            return False
        
        position = self.positions[symbol]
        current_price = self.current_prices.get(symbol, 0)
        
        # Calculate unrealized P&L percentage
        if position.avg_price > 0 and current_price > 0:
            if position.side == 'long':
                pnl_pct = (current_price - position.avg_price) / position.avg_price
            else:  # short position
                pnl_pct = (position.avg_price - current_price) / position.avg_price
            
            # Scale the threshold based on averaging level
            # For level 1, use the base threshold
            # For higher levels, scale it (e.g., level 10 would be -10%)
            scaled_threshold = self.averaging_down_threshold
            if self.current_averaging_level > 0:
                # Scale threshold based on level (e.g., level 10 = -10%)
                scaled_threshold = -0.01 * (self.current_averaging_level + 1)
            
            # Check if we're at a loss beyond the scaled threshold and haven't exceeded max levels
            if (pnl_pct <= scaled_threshold and 
                self.current_averaging_level < self.max_averaging_levels):
                print(f"Averaging down threshold for level {self.current_averaging_level+1}: {scaled_threshold:.2%}")
                return True
        
        return False
        
    def is_at_peak(self, symbol: str, current_price: float) -> bool:
        """Determine if current price is at a local peak"""
        if len(self.price_history[symbol]) < self.peak_tracking_window:
            return False
        
        recent_prices = self.price_history[symbol][-self.peak_tracking_window:]
        recent_max = max(recent_prices)
        
        # Initialize peak tracking for this symbol if not already done
        if symbol not in self.recent_peaks:
            self.recent_peaks[symbol] = current_price
            self.peak_confirmation_counts[symbol] = 0
        
        # Update recent peak
        if current_price >= recent_max:
            self.recent_peaks[symbol] = current_price
            self.peak_confirmation_counts[symbol] = 0
            return False
        
        # Check if price is declining from peak
        if current_price < self.recent_peaks[symbol]:
            self.peak_confirmation_counts[symbol] += 1
            if self.peak_confirmation_counts[symbol] >= self.required_peak_confirmations:
                print(f"📉 Peak detected for {symbol}: ${self.recent_peaks[symbol]:.4f} -> ${current_price:.4f}")
                return True
        
        return False
    
    def check_exit_conditions(self, symbol: str) -> Tuple[bool, float]:
        """Check if exit conditions are met for a position
        Returns a tuple of (should_exit, profit_cents)
        """
        if symbol not in self.positions:
            return False, 0.0
            
        position = self.positions[symbol]
        current_price = self.current_prices.get(symbol, 0)
        
        if position.avg_price > 0 and current_price > 0:
            if position.side == 'long':
                profit_cents = (current_price - position.avg_price) * 100  # Convert to cents
                pnl_pct = (current_price - position.avg_price) / position.avg_price
            else:  # short position
                profit_cents = (position.avg_price - current_price) * 100  # Convert to cents
                pnl_pct = (position.avg_price - current_price) / position.avg_price
                
            # Exit if profit target in cents is reached
            if profit_cents >= self.profit_target_cents:
                print(f"🎯 Profit target reached for {symbol}: ${profit_cents:.2f} cents (${current_price:.2f} - ${position.avg_price:.2f})")
                return True, profit_cents
            
            # Exit if we're at a peak with minimum profit
            if pnl_pct >= self.min_peak_profit and self.is_at_peak(symbol, current_price):
                print(f"📈 Taking profit at peak for {symbol}: {pnl_pct:.2%} (${profit_cents:.2f} cents)")
                return True, profit_cents
                
        return False, 0.0
        
    def track_closed_position(self, symbol: str, exit_price: float, profit_cents: float):
        """Track a closed position for potential re-entry"""
        if not self.enable_reentry:
            return
            
        # Create a new closed position entry
        closed_position = ClosedPosition(
            symbol=symbol,
            exit_price=exit_price,
            exit_time=datetime.now(),
            profit_cents=profit_cents
        )
        
        self.closed_positions[symbol] = closed_position
        print(f"🔍 Tracking {symbol} for re-entry opportunity after closing at ${exit_price:.2f}")
        
        # Start a timer to stop monitoring after the specified time
        self.reentry_timers[symbol] = threading.Timer(
            self.reentry_monitoring_time, 
            self.stop_reentry_monitoring,
            args=[symbol]
        )
        self.reentry_timers[symbol].daemon = True
        self.reentry_timers[symbol].start()
        
    def stop_reentry_monitoring(self, symbol: str):
        """Stop monitoring a closed position for re-entry"""
        if symbol in self.closed_positions:
            self.closed_positions[symbol].monitoring_active = False
            print(f"⏱️ Stopped monitoring {symbol} for re-entry (timeout reached)")
            
            # Clean up after some time to avoid memory leaks
            def remove_closed_position():
                if symbol in self.closed_positions:
                    del self.closed_positions[symbol]
                if symbol in self.reentry_timers:
                    del self.reentry_timers[symbol]
                    
            cleanup_timer = threading.Timer(60, remove_closed_position)
            cleanup_timer.daemon = True
            cleanup_timer.start()
    
    def execute_trading_strategy(self, symbol: str, current_price: float):
        """Execute the trading strategy for a symbol"""
        # Update current price
        self.current_prices[symbol] = current_price
        
        # Check for re-entry opportunities for closed positions
        self.check_reentry_opportunities(symbol, current_price)
        
        # Check if we have a position for this symbol
        if symbol in self.positions:
            position = self.positions[symbol]
            
            # Update position's market value and unrealized P&L
            if position.side == 'long':
                position.market_value = position.qty * current_price
                position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
            else:  # short position
                position.market_value = position.qty * current_price
                position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
            
            # Check if we should exit the position
            should_exit, profit_cents = self.check_exit_conditions(symbol)
            if should_exit:
                # Place order to close position
                close_side = OrderSide.SELL if position.side == 'long' else OrderSide.BUY
                execution = self.place_market_order(symbol, position.qty, close_side)
                
                if execution and execution.status == 'filled':
                    # Track the closed position for potential re-entry
                    self.track_closed_position(symbol, current_price, profit_cents)
                
                del self.positions[symbol]
                self.current_averaging_level = 0  # Reset averaging level
                return
            
            # Check if we should average down
            if self.should_average_down(symbol):
                self.current_averaging_level += 1
                avg_qty = self.calculate_averaging_quantity()
                
                print(f"📉 Averaging down on {symbol} (Level {self.current_averaging_level})")
                print(f"   Current Price: ${current_price:.2f} | Avg Price: ${position.avg_price:.2f}")
                print(f"   P&L: ${position.unrealized_pl:.2f} | Adding {avg_qty} shares")
                
                # Place order for averaging down
                avg_side = OrderSide.BUY if position.side == 'long' else OrderSide.SELL
                execution = self.place_market_order(symbol, avg_qty, avg_side)
                
                if execution and execution.status == 'filled':
                    # Update position with new average price and quantity
                    new_qty = position.qty + avg_qty
                    new_avg_price = ((position.qty * position.avg_price) + 
                                    (avg_qty * execution.actual_price)) / new_qty
                    
                    position.qty = new_qty
                    position.avg_price = new_avg_price
                    position.market_value = position.qty * current_price
                    
                    # Recalculate unrealized P&L
                    if position.side == 'long':
                        position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                    else:  # short position
                        position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
    
    def check_reentry_opportunities(self, symbol: str, current_price: float):
        """Check for re-entry opportunities for closed positions"""
        if not self.enable_reentry or symbol not in self.closed_positions:
            return
            
        closed_position = self.closed_positions[symbol]
        
        # Skip if monitoring is no longer active
        if not closed_position.monitoring_active:
            return
            
        # Add current price to history
        closed_position.price_history_after_close.append(current_price)
        
        # Calculate price drop percentage since closing
        price_change_pct = (current_price - closed_position.exit_price) / closed_position.exit_price
        
        # Check if price has dropped enough for re-entry
        if price_change_pct <= self.reentry_price_drop_pct:
            print(f"🔄 Re-entry opportunity detected for {symbol}")
            print(f"   Exit Price: ${closed_position.exit_price:.2f} | Current Price: ${current_price:.2f}")
            print(f"   Price Drop: {price_change_pct:.2%} | Target: {self.reentry_price_drop_pct:.2%}")
            
            # Place order to re-enter position
            qty = self.initial_qty  # Start with initial quantity
            side = OrderSide.BUY  # Always re-enter with a long position for now
            
            execution = self.place_market_order(symbol, qty, side)
            if execution and execution.status == 'filled':
                print(f"✅ Re-entered {symbol} at ${execution.actual_price:.2f} with {qty} shares")
                
                # Stop monitoring this closed position
                closed_position.monitoring_active = False
                
                # Clean up
                if symbol in self.reentry_timers and self.reentry_timers[symbol].is_alive():
                    self.reentry_timers[symbol].cancel()
                    
                # Remove from closed positions after a delay
                def remove_closed_position():
                    if symbol in self.closed_positions:
                        del self.closed_positions[symbol]
                    if symbol in self.reentry_timers:
                        del self.reentry_timers[symbol]
                        
                cleanup_timer = threading.Timer(5, remove_closed_position)
                cleanup_timer.daemon = True
                cleanup_timer.start()
                
        # If no position exists for this symbol, open a new position
        elif symbol not in self.positions:
            # Place a new buy order with initial quantity
            print(f"🔵 Opening new position for {symbol} at ${current_price:.2f}")
            print(f"   Quantity: {self.initial_qty} shares")
            
            # Place order to open position (long side)
            execution = self.place_market_order(symbol, self.initial_qty, OrderSide.BUY)
            
            if execution and execution.status == 'filled':
                # Create new position
                position = Position(
                    symbol=symbol,
                    qty=self.initial_qty,
                    side='long',
                    avg_price=execution.actual_price,
                    market_value=self.initial_qty * current_price,
                    unrealized_pl=0.0,
                    entry_time=datetime.now()
                )
                
                # Add to positions dictionary
                self.positions[symbol] = position
                self.current_averaging_level = 0  # Reset averaging level
                
                print(f"✅ New position opened: {position.qty} shares of {symbol} @ ${position.avg_price:.2f}")
    
    def emergency_close_all_positions(self):
        """Emergency close all positions"""
        if self.emergency_close_in_progress:
            print("⚠️ Emergency close already in progress...")
            return
            
        self.emergency_close_in_progress = True
        print("\n🚨 EMERGENCY CLOSE INITIATED - CLOSING ALL POSITIONS 🚨")
        
        try:
            # Get current positions from Alpaca
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                positions_data = response.json()
                
                if not positions_data:
                    print("No positions to close")
                    self.emergency_close_in_progress = False
                    return
                    
                print(f"Closing {len(positions_data)} positions...")
                
                total_pnl = 0.0
                
                for pos in positions_data:
                    symbol = pos['symbol']
                    qty = abs(int(float(pos['qty'])))
                    side = 'sell' if pos['side'] == 'long' else 'buy'
                    unrealized_pl = float(pos['unrealized_pl'])
                    
                    print(f"Closing {qty} shares of {symbol} ({side})")
                    
                    # Place market order to close position
                    order_side = OrderSide.SELL if side == 'sell' else OrderSide.BUY
                    execution = self.place_market_order(symbol, qty, order_side)
                    
                    if execution and execution.status == 'filled':
                        total_pnl += unrealized_pl
                        
                        # Remove from our internal positions tracking
                        if symbol in self.positions:
                            del self.positions[symbol]
                            
                print(f"\n✅ Emergency close complete. Total P&L: ${total_pnl:.2f}")
            else:
                print(f"Failed to get positions: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error during emergency close: {e}")
            print(f"Error during emergency close: {e}")
            
        finally:
            self.emergency_close_in_progress = False
            
    def display_position_summary(self):
        """Display a summary of current positions"""
        try:
            # Get fresh position data from Alpaca
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)
            
            # Also display closed positions being monitored for re-entry
            if self.enable_reentry and self.closed_positions:
                print("\n📊 Closed Positions Being Monitored for Re-entry:")
                print("-" * 80)
                print(f"{'Symbol':<10} {'Exit Price':>10} {'Current':>10} {'Change %':>10} {'Target %':>10} {'Time Left':>15}")
                print("-" * 80)
                
                current_time = datetime.now()
                
                for symbol, closed_pos in self.closed_positions.items():
                    if not closed_pos.monitoring_active:
                        continue
                        
                    current_price = self.current_prices.get(symbol, 0)
                    if current_price > 0:
                        price_change_pct = (current_price - closed_pos.exit_price) / closed_pos.exit_price * 100
                        
                        # Calculate time left for monitoring
                        elapsed_seconds = (current_time - closed_pos.exit_time).total_seconds()
                        remaining_seconds = max(0, self.reentry_monitoring_time - elapsed_seconds)
                        time_left = f"{int(remaining_seconds // 60)}m {int(remaining_seconds % 60)}s"
                        
                        print(f"{symbol:<10} ${closed_pos.exit_price:>9.2f} ${current_price:>9.2f} {price_change_pct:>9.2f}% {self.reentry_price_drop_pct*100:>9.2f}% {time_left:>15}")
                
                print("-" * 80)
            
            if response.status_code == 200:
                positions_data = response.json()
                
                if not positions_data:
                    print("\n📊 No open positions")
                    return
                    
                print("\n" + "=" * 80)
                print("📊 POSITION SUMMARY")
                print("=" * 80)
                print(f"{'Symbol':<10} {'Side':<6} {'Quantity':<10} {'Avg Price':<12} {'Current':<12} {'Market Value':<15} {'Unrealized P&L':<15} {'P&L %':<10}")
                print("-" * 80)
                
                total_market_value = 0.0
                total_unrealized_pl = 0.0
                
                for pos in positions_data:
                    symbol = pos['symbol']
                    side = pos['side']
                    qty = int(float(pos['qty']))
                    avg_price = float(pos['avg_entry_price'])
                    current_price = float(pos['current_price'])
                    market_value = float(pos['market_value'])
                    unrealized_pl = float(pos['unrealized_pl'])
                    
                    # Calculate P&L percentage
                    if avg_price > 0:
                        if side == 'long':
                            pnl_pct = (current_price - avg_price) / avg_price
                        else:  # short
                            pnl_pct = (avg_price - current_price) / avg_price
                    else:
                        pnl_pct = 0.0
                        
                    # Format the row
                    print(f"{symbol:<10} {side:<6} {qty:<10} ${avg_price:<11.2f} ${current_price:<11.2f} ${market_value:<14.2f} ${unrealized_pl:<14.2f} {pnl_pct:<9.2%}")
                    
                    total_market_value += market_value
                    total_unrealized_pl += unrealized_pl
                    
                print("-" * 80)
                print(f"{'TOTAL':<40} ${total_market_value:<14.2f} ${total_unrealized_pl:<14.2f}")
                print("=" * 80)
            else:
                print(f"Failed to get positions: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error displaying position summary: {e}")
            print(f"Error displaying position summary: {e}")
            
    def keyboard_listener(self):
        """Listen for keyboard commands"""
        if os.name == 'nt':  # Windows
            self.windows_keyboard_listener()
        else:  # Unix/Linux/Mac
            self.unix_keyboard_listener()
            
    def windows_keyboard_listener(self):
        """Windows-specific keyboard listener using msvcrt"""
        import msvcrt
        
        print("\n⌨️  Keyboard controls active:")
        print("   'q' - Emergency close all positions")
        print("   'p' - Display position summary")
        print("   's' - Stop trading")
        print("   'a' - Add to position")
        
        if self.enable_reentry:
            print("\n🔄 Re-entry feature is active:")
            print(f"   - After taking profit, positions will be monitored for {self.reentry_monitoring_time} seconds")
            print(f"   - Re-entry will trigger if price drops by {abs(self.reentry_price_drop_pct)*100:.1f}% from exit price")
        
        self.keyboard_listener_active = True
        
        while self.keyboard_listener_active:
            if msvcrt.kbhit():
                key = msvcrt.getch().decode('utf-8').lower()
                
                if key == 'q':
                    print("\n🚨 Emergency close triggered by keyboard")
                    self.emergency_close_all_positions()
                elif key == 'p':
                    self.display_position_summary()
                elif key == 's':
                    print("\n🛑 Stopping trading...")
                    self.stop_trading()
                elif key == 'a':
                    # Clear any remaining characters in the buffer before handling input
                    while msvcrt.kbhit():
                        msvcrt.getch()
                    self.handle_add_position_input()
                    # Clear any characters that might have been pressed during input
                    while msvcrt.kbhit():
                        msvcrt.getch()
                    print("\n⌨️  Keyboard controls active again. Press 'a' to add another position.")
                    
            time.sleep(0.1)  # Prevent high CPU usage
            
    def unix_keyboard_listener(self):
        """Unix-specific keyboard listener using sys and tty"""
        import sys
        import tty
        import termios
        
        print("\n⌨️  Keyboard controls active:")
        print("   'q' - Emergency close all positions")
        print("   'p' - Display position summary")
        print("   's' - Stop trading")
        print("   'a' - Add to position")
        
        if self.enable_reentry:
            print("\n🔄 Re-entry feature is active:")
            print(f"   - After taking profit, positions will be monitored for {self.reentry_monitoring_time} seconds")
            print(f"   - Re-entry will trigger if price drops by {abs(self.reentry_price_drop_pct)*100:.1f}% from exit price")
        
        self.keyboard_listener_active = True
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)
        
        try:
            tty.setraw(fd)
            while self.keyboard_listener_active:
                key = sys.stdin.read(1).lower()
                
                if key == 'q':
                    print("\n🚨 Emergency close triggered by keyboard")
                    self.emergency_close_all_positions()
                elif key == 'p':
                    self.display_position_summary()
                elif key == 's':
                    print("\n🛑 Stopping trading...")
                    self.stop_trading()
                elif key == 'a':
                    # Need to restore terminal settings before getting input
                    termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
                    self.handle_add_position_input()
                    # Set back to raw mode
                    tty.setraw(fd)
                    
                time.sleep(0.1)  # Prevent high CPU usage
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
            
    def start_keyboard_listener(self):
        """Start keyboard listener in a separate thread"""
        keyboard_thread = threading.Thread(target=self.keyboard_listener)
        keyboard_thread.daemon = True  # Thread will exit when main program exits
        keyboard_thread.start()
        
    def stop_keyboard_listener(self):
        """Stop the keyboard listener"""
        self.keyboard_listener_active = False

    def calculate_averaging_quantity(self) -> int:
        """Calculate quantity for averaging down"""
        return self.initial_qty * (2 ** self.current_averaging_level)
        
    def on_message(self, ws, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)
            
            # Check if data is a list (some Alpaca messages come as lists)
            if isinstance(data, list):
                # Process each item in the list
                for item in data:
                    self._process_message_item(item)
            else:
                # Process single message
                self._process_message_item(data)
                    
        except json.JSONDecodeError:
            logger.error(f"Failed to parse WebSocket message: {message}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
            
    def _process_message_item(self, data):
        """Process a single message item"""
        try:
            # Handle different message types
            if not isinstance(data, dict):
                logger.warning(f"Received non-dictionary message: {data}")
                return
                
            msg_type = data.get('T')
            
            if msg_type == 'success' and data.get('msg') == 'authenticated':
                self.authenticated = True
                print("✅ WebSocket authenticated successfully")
                self.subscribe_to_trades()
                
            elif msg_type == 'subscription':
                print(f"✅ Successfully subscribed to {data.get('trades', [])}")
                
            elif msg_type == 'error':
                logger.error(f"WebSocket error: {data}")
                print(f"❌ WebSocket error: {data.get('msg', 'Unknown error')}")
                
            elif msg_type == 't':
                # Process trade data
                symbol = data.get('S')
                price = float(data.get('p', 0))
                size = int(data.get('s', 0))
                timestamp = data.get('t')
                
                if symbol and price > 0:
                    # Update current price
                    self.current_prices[symbol] = price
                    
                    # Store price history (limited to last 100 prices)
                    self.price_history[symbol].append(price)
                    if len(self.price_history[symbol]) > 100:
                        self.price_history[symbol].pop(0)
                    
                    # Display real-time price updates and PnL calculations
                    if symbol in self.positions:
                        position = self.positions[symbol]
                        # Update position's market value and unrealized P&L
                        if position.side == 'long':
                            position.market_value = position.qty * price
                            position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                            pnl_pct = (price - position.avg_price) / position.avg_price * 100
                        else:  # short position
                            position.market_value = position.qty * price
                            position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
                            pnl_pct = (position.avg_price - price) / position.avg_price * 100
                        
                        # Display price update with PnL information
                        print(f"💹 {symbol}: ${price:.4f} (${position.unrealized_pl:.2f} | {pnl_pct:.2f}%)", end="\r")
                    else:
                        # Just display price update if no position
                        print(f"💹 {symbol}: ${price:.4f}", end="\r")
                    
                    # Execute trading strategy
                    self.execute_trading_strategy(symbol, price)
        except Exception as e:
            logger.error(f"Error processing message item: {e}")
            logger.error(f"Message data: {data}")

            
    def authenticate_ws(self):
        """Authenticate with Alpaca WebSocket"""
        auth_data = {
            "action": "auth",
            "key": self.api_key,
            "secret": self.secret_key
        }
        self.ws.send(json.dumps(auth_data))
        
    def subscribe_to_trades(self):
        """Subscribe to trade updates for our symbols"""
        sub_data = {
            "action": "subscribe",
            "trades": self.symbols
        }
        self.ws.send(json.dumps(sub_data))
        print(f"📊 Subscribing to trade data for: {', '.join(self.symbols)}")
        
    def on_error(self, ws, error):
        """Handle WebSocket errors"""
        logger.error(f"WebSocket error: {error}")
        print(f"❌ WebSocket error: {error}")
        
    def on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket connection close"""
        self.is_connected = False
        self.authenticated = False
        logger.info("WebSocket connection closed")
        print("📴 WebSocket connection closed")
        
    def on_open(self, ws):
        """Handle WebSocket connection open"""
        self.is_connected = True
        logger.info("WebSocket connection established")
        print("📲 WebSocket connection established")
        self.authenticate_ws()
        
    def sync_positions_with_alpaca(self):
        """Sync internal position tracking with actual positions from Alpaca API"""
        try:
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                positions_data = response.json()
                alpaca_positions = {}
                
                # Process positions from Alpaca API
                for pos_data in positions_data:
                    symbol = pos_data['symbol']
                    
                    # Handle different field names for average cost
                    avg_cost = 0.0
                    if 'avg_entry_price' in pos_data:
                        avg_cost = float(pos_data['avg_entry_price'])
                    elif 'avg_price' in pos_data:
                        avg_cost = float(pos_data['avg_price'])
                    elif 'avg_cost' in pos_data:
                        avg_cost = float(pos_data['avg_cost'])
                    else:
                        logger.warning(f"Could not find average cost field for {symbol}. Using current price.")
                        avg_cost = self.current_prices.get(symbol, 0.0)
                    
                    # Create position object
                    position = Position(
                        symbol=symbol,
                        qty=int(float(pos_data['qty'])),
                        side=pos_data['side'],
                        avg_price=avg_cost,
                        market_value=float(pos_data.get('market_value', 0.0)),
                        unrealized_pl=float(pos_data.get('unrealized_pl', 0.0)),
                        entry_time=datetime.now()  # Alpaca doesn't provide entry time in positions
                    )
                    alpaca_positions[symbol] = position
                
                # Check for new positions or updated positions
                for symbol, position in alpaca_positions.items():
                    if symbol not in self.positions:
                        # New position detected (likely manually opened)
                        self.positions[symbol] = position
                        print(f"\n🔍 Detected manually opened position: {symbol}")
                        print(f"   {position.qty} shares @ ${position.avg_price:.2f} | Side: {position.side}")
                    else:
                        # Position exists, check if quantity or average price changed
                        existing_pos = self.positions[symbol]
                        if existing_pos.qty != position.qty or abs(existing_pos.avg_price - position.avg_price) > 0.01:
                            # Position was modified manually
                            old_qty = existing_pos.qty
                            old_avg = existing_pos.avg_price
                            self.positions[symbol] = position
                            print(f"\n🔄 Position updated for {symbol}:")
                            print(f"   Old: {old_qty} shares @ ${old_avg:.2f}")
                            print(f"   New: {position.qty} shares @ ${position.avg_price:.2f}")
                
                # Check for closed positions
                closed_symbols = [s for s in self.positions if s not in alpaca_positions]
                for symbol in closed_symbols:
                    print(f"\n❌ Position closed for {symbol} (not found in Alpaca)")
                    del self.positions[symbol]
                
                return True
            else:
                logger.error(f"Failed to sync positions: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error syncing positions: {e}")
            return False
            
    def handle_add_position_input(self):
        """Get user input for adding to a position and execute the order"""
        print("\n📈 Add to Position")
        
        # Display current positions first
        self.display_position_summary()
        
        # If we have only one position, use that symbol automatically
        if len(self.positions) == 1:
            symbol = list(self.positions.keys())[0]
            print(f"\nUsing current position: {symbol}")
        # If we have multiple positions, ask which one to add to
        elif len(self.positions) > 1:
            position_symbols = list(self.positions.keys())
            for i, sym in enumerate(position_symbols):
                print(f"{i+1}. {sym} - {self.positions[sym].qty} shares @ ${self.positions[sym].avg_price:.2f}")
            
            try:
                choice = input("\nSelect position number to add to (or press Enter to cancel): ").strip()
                if not choice:
                    print("Operation cancelled.")
                    return
                    
                choice_idx = int(choice) - 1
                if choice_idx < 0 or choice_idx >= len(position_symbols):
                    print("Invalid selection.")
                    return
                    
                symbol = position_symbols[choice_idx]
            except ValueError:
                print("Invalid selection. Please enter a number.")
                return
        # If no positions, ask for symbol
        else:
            symbol = input("\nEnter symbol (or press Enter to cancel): ").strip().upper()
            if not symbol:
                print("Operation cancelled.")
                return
                
            # Check if symbol is in tracked symbols
            if symbol not in self.symbols:
                add_to_tracked = input(f"{symbol} is not in tracked symbols. Add it? (y/n): ").strip().lower()
                if add_to_tracked == 'y':
                    self.symbols.append(symbol)
                    print(f"Added {symbol} to tracked symbols")
                    # Subscribe to trades for this symbol
                    if self.is_connected and self.authenticated:
                        sub_data = {
                            "action": "subscribe",
                            "trades": [symbol]
                        }
                        self.ws.send(json.dumps(sub_data))
                        print(f"Subscribed to trade data for {symbol}")
                else:
                    print("Operation cancelled.")
                    return
        
        # Calculate suggested quantity (2x current position or initial_qty if no position)
        suggested_qty = self.initial_qty
        if symbol in self.positions:
            suggested_qty = self.positions[symbol].qty * 2
        
        # Get quantity input with suggested value
        try:
            qty_input = input(f"Enter quantity to add to {symbol} (suggested: {suggested_qty}, press Enter to use suggested): ").strip()
            if not qty_input:
                additional_qty = suggested_qty
                print(f"Using suggested quantity: {additional_qty}")
            else:
                additional_qty = int(qty_input)
                
            if additional_qty <= 0:
                print("Quantity must be positive.")
                return
        except ValueError:
            print("Invalid quantity. Please enter a number.")
            return
            
        # Confirm the operation
        try:
            current_price = self.current_prices.get(symbol)
            if not current_price or current_price <= 0:
                print(f"Could not get current price for {symbol}. Waiting for price data...")
                # Wait a bit for price data
                for _ in range(5):  # Try for 5 seconds
                    time.sleep(1)
                    current_price = self.current_prices.get(symbol)
                    if current_price and current_price > 0:
                        break
                        
                if not current_price or current_price <= 0:
                    print(f"Still no price data for {symbol}. Cannot proceed.")
                    return
                
            estimated_cost = current_price * additional_qty
            confirm = input(f"Add {additional_qty} shares of {symbol} at ~${current_price:.2f} (est. ${estimated_cost:.2f})? (y/n): ").strip().lower()
            
            if confirm != 'y':
                print("Operation cancelled.")
                return
                
            # Execute the order
            success = self.add_to_position(symbol, additional_qty, force_sync=True)
            
            if success:
                print(f"✅ Successfully added {additional_qty} shares of {symbol}")
            else:
                print(f"❌ Failed to add to position for {symbol}")
                
        except Exception as e:
            print(f"Error: {e}")
    
    def add_to_position(self, symbol: str, additional_qty: int, force_sync: bool = True):
        """Manually add to an existing position at current market price
        
        Args:
            symbol: The stock symbol to add to
            additional_qty: Number of shares to add
            force_sync: Whether to force an immediate sync with Alpaca after the order
        
        Returns:
            bool: True if successful, False otherwise
        """
        if symbol not in self.symbols:
            print(f"❌ Error: {symbol} is not in the list of tracked symbols: {', '.join(self.symbols)}")
            return False
            
        if additional_qty <= 0:
            print("❌ Error: Additional quantity must be positive")
            return False
            
        current_price = self.current_prices.get(symbol)
        if not current_price or current_price <= 0:
            print(f"❌ Error: No valid current price for {symbol}. Please wait for price data.")
            return False
            
        # Check if we already have a position for this symbol
        position_exists = symbol in self.positions
        position_side = 'long' if position_exists else None
        if position_exists:
            position_side = self.positions[symbol].side
            print(f"\n🔍 Adding to existing {position_side} position for {symbol}")
            print(f"   Current: {self.positions[symbol].qty} shares @ ${self.positions[symbol].avg_price:.2f}")
            print(f"   Adding: {additional_qty} shares @ ~${current_price:.2f} (market price)")
        else:
            print(f"\n🔍 Opening new position for {symbol}")
            print(f"   Quantity: {additional_qty} shares @ ~${current_price:.2f} (market price)")
            position_side = 'long'  # Default to long for new positions
            
        # Place the order
        side = OrderSide.BUY if position_side == 'long' else OrderSide.SELL
        execution = self.place_market_order(symbol, additional_qty, side)
        
        if execution and execution.status == 'filled':
            # If this was a new position, it will be created by sync_positions_with_alpaca
            # If it was an existing position, we'll update our local tracking immediately
            if position_exists:
                position = self.positions[symbol]
                old_qty = position.qty
                old_avg_price = position.avg_price
                new_qty = position.qty + additional_qty
                
                # Calculate new average price
                new_avg_price = ((old_qty * old_avg_price) + 
                               (additional_qty * execution.actual_price)) / new_qty
                
                # Update the position
                position.qty = new_qty
                position.avg_price = new_avg_price
                position.market_value = position.qty * current_price
                
                # Recalculate unrealized P&L
                if position.side == 'long':
                    position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                    position.pnl_percentage = (current_price - position.avg_price) / position.avg_price
                else:  # short position
                    position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
                    position.pnl_percentage = (position.avg_price - current_price) / position.avg_price
                
                print(f"✅ Position updated: {position.qty} shares @ ${position.avg_price:.2f}")
                print(f"   Old: {old_qty} shares @ ${old_avg_price:.2f}")
                print(f"   New P&L: ${position.unrealized_pl:.2f} ({position.pnl_percentage:.2%})")
            else:
                # Create a new position object immediately
                new_position = Position(
                    symbol=symbol,
                    qty=additional_qty,
                    side=position_side,
                    avg_price=execution.actual_price,
                    market_value=additional_qty * current_price,
                    unrealized_pl=0.0,  # Will be calculated below
                    entry_time=datetime.now()
                )
                
                # Calculate initial P&L
                if position_side == 'long':
                    new_position.unrealized_pl = (current_price - execution.actual_price) * additional_qty
                    new_position.pnl_percentage = (current_price - execution.actual_price) / execution.actual_price
                else:  # short
                    new_position.unrealized_pl = (execution.actual_price - current_price) * additional_qty
                    new_position.pnl_percentage = (execution.actual_price - current_price) / execution.actual_price
                
                # Add to our positions dictionary
                self.positions[symbol] = new_position
                print(f"✅ New position created: {additional_qty} shares @ ${execution.actual_price:.2f}")
            
            # Force a sync with Alpaca if requested
            if force_sync:
                print("🔄 Forcing position sync with Alpaca...")
                self.sync_positions_with_alpaca()
                
            return True
        else:
            print(f"❌ Failed to add to position: {execution.status if execution else 'Unknown error'}")
            return False
    
    def start_position_sync_timer(self):
        """Start a timer to periodically sync positions with Alpaca API"""
        # Cancel any existing timer
        if self.position_sync_timer:
            self.position_sync_timer.cancel()
        
        # Define a function to run the sync and schedule the next run
        def sync_and_reschedule():
            print(f"\n🔄 Syncing positions with Alpaca...")
            
            # Print position details and profit target status
            for symbol, position in self.positions.items():
                current_price = self.current_prices.get(symbol, 0)
                if current_price > 0 and position.avg_price > 0:
                    pnl_pct = (current_price - position.avg_price) / position.avg_price
                    profit_cents = (current_price - position.avg_price) * 100 if position.side == 'long' else (position.avg_price - current_price) * 100
                    profit_target_status = f"✅ Profit target reached ({profit_cents:.2f}/{self.profit_target_cents:.2f} cents)" if profit_cents >= self.profit_target_cents else f"⏳ Profit target: {profit_cents:.2f}/{self.profit_target_cents:.2f} cents"
                    print(f"   {symbol}: Qty:{position.qty} @ ${position.avg_price:.2f} | Current: ${current_price:.2f} | {profit_target_status}")
            
            self.sync_positions_with_alpaca()
            # Reschedule if still connected
            if self.is_connected:
                self.position_sync_timer = threading.Timer(self.position_sync_interval, sync_and_reschedule)
                self.position_sync_timer.daemon = True
                self.position_sync_timer.start()
        
        # Start the initial timer
        self.position_sync_timer = threading.Timer(self.position_sync_interval, sync_and_reschedule)
        self.position_sync_timer.daemon = True
        self.position_sync_timer.start()
        print(f"📊 Position sync timer started (every {self.position_sync_interval} seconds)")
    
    def start_trading(self):
        """Start the trading bot"""
        print("\n🚀 Starting Enhanced Alpaca Trading Bot with Averaging Down...")
        print(f"📊 Trading Configuration:")
        print(f"   Symbols: {', '.join(self.symbols)}")
        print(f"   Profit Target: {self.profit_target_cents:.2f} cents")
        print(f"   Initial Quantity: {self.initial_qty}")
        print(f"   Max Averaging Levels: {self.max_averaging_levels}")
        print(f"   Averaging Down Threshold: {self.averaging_down_threshold:.2%}")
        print(f"   Using {'Paper' if self.use_paper else 'Live'} Trading")
        
        if self.enable_reentry:
            print("\n🔄 Re-entry Strategy Enabled:")
            print(f"   - After taking profit, positions will be monitored for {self.reentry_monitoring_time} seconds")
            print(f"   - Re-entry will trigger if price drops by {abs(self.reentry_price_drop_pct)*100:.1f}% from exit price")
        
        # Get account information
        self.get_account_info()
        
        # Get current positions
        self.get_positions()
        
        # Start keyboard listener for emergency controls
        self.start_keyboard_listener()
        
        # Start position sync timer
        self.start_position_sync_timer()
        
        # Connect to WebSocket for real-time data
        print("\n🔌 Connecting to Alpaca WebSocket...")
        
        # Initialize WebSocket
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_open=lambda ws: self.on_open(ws),
            on_message=lambda ws, msg: self.on_message(ws, msg),
            on_error=lambda ws, err: self.on_error(ws, err),
            on_close=lambda ws, close_status_code, close_msg: self.on_close(ws, close_status_code, close_msg)
        )
        
        # Start WebSocket in a separate thread
        self.ws_thread = threading.Thread(target=self.ws.run_forever, daemon=True)
        self.ws_thread.start()
        
        # Wait for WebSocket connection
        max_wait = 10
        waited = 0
        while waited < max_wait and not (self.is_connected and self.authenticated):
            time.sleep(0.5)
            waited += 0.5
        
        if not self.is_connected or not self.authenticated:
            print("❌ Failed to connect to WebSocket feed")
            return None
        
        print("\n✅ Trading bot started successfully!")
        print("Press 'q' for emergency close, 'p' for position summary, 's' to stop trading, 'a' to add to position")
        print("Press Ctrl+C to stop the bot gracefully")
        
        return self.ws_thread

    def stop_trading(self):
        """Stop trading and clean up resources"""
        print("\n🛑 Stopping trading...")
        
        # Stop keyboard listener
        self.stop_keyboard_listener()
        
        # Stop position sync timer
        if self.position_sync_timer:
            self.position_sync_timer.cancel()
            self.position_sync_timer = None
            print("📊 Position sync timer stopped")
        
        # Close WebSocket connection
        if self.ws:
            self.ws.close()
            
        # Print performance summary
        self.print_performance_summary()
        
        # Set flag to indicate bot is stopped
        self.is_connected = False
        self.authenticated = False
        
    def print_performance_summary(self):
        """Print a summary of trading performance"""
        print("\n" + "=" * 60)
        print("📈 TRADING PERFORMANCE SUMMARY")
        print("=" * 60)
        print(f"Total Trades: {self.performance.total_trades}")
        print(f"Successful Trades: {self.performance.successful_trades}")
        
        if self.performance.successful_trades > 0:
            success_rate = self.performance.successful_trades / self.performance.total_trades * 100
            print(f"Success Rate: {success_rate:.1f}%")
            print(f"Average Execution Time: {self.performance.avg_execution_time_ms:.2f}ms")
            print(f"Min Execution Time: {self.performance.min_execution_time_ms:.2f}ms")
            print(f"Max Execution Time: {self.performance.max_execution_time_ms:.2f}ms")
            print(f"Average Slippage: ${self.performance.avg_slippage:.4f}")
            print(f"Total Slippage: ${self.performance.total_slippage:.4f}")
        
        print("=" * 60)

def main():
    """Main function to run the enhanced trading bot"""
    API_KEY = os.getenv('APCA_API_KEY_ID')
    SECRET_KEY = os.getenv('APCA_API_SECRET_KEY')
    
    if not API_KEY or not SECRET_KEY:
        print("❌ Please set your Alpaca API credentials in environment variables:")
        print("APCA_API_KEY_ID and APCA_API_SECRET_KEY")
        return
    
    # Trading configuration
    symbols = ["AAPL"]  # Symbols to trade
    profit_target_cents = 15  # $0.15 profit target
    use_paper = True  # Use paper trading
    
    # Averaging down configuration
    initial_qty = 100
    max_averaging_levels = 6
    averaging_down_threshold = -0.01  # .10% loss threshold
    
    # Re-entry strategy configuration
    enable_reentry = True  # Enable re-entry strategy
    reentry_price_drop_pct = -0.005  # Re-enter if price drops by 0.5% from exit price
    reentry_monitoring_time = 10  # Monitor for 5 minutes (300 seconds) after closing position
    
    # Create and start trading bot
    bot = EnhancedAlpacaBot(
        api_key=API_KEY,
        secret_key=SECRET_KEY,
        symbols=symbols,
        use_paper=use_paper,
        profit_target_cents=profit_target_cents,
        initial_qty=initial_qty,
        max_averaging_levels=max_averaging_levels,
        averaging_down_threshold=averaging_down_threshold,
        enable_reentry=enable_reentry,
        reentry_price_drop_pct=reentry_price_drop_pct,
        reentry_monitoring_time=reentry_monitoring_time
    )
    
    try:
        ws_thread = bot.start_trading()
        if ws_thread:
            # Instead of just joining the thread, we'll use a loop that can be interrupted
            running = True
            while running:
                try:
                    # Check if thread is still alive every second
                    ws_thread.join(1.0)
                    if not ws_thread.is_alive():
                        running = False
                except KeyboardInterrupt:
                    print("\n⚠️ Keyboard interrupt detected. Stopping bot gracefully...")
                    bot.stop_trading()
                    running = False
    except Exception as e:
        print(f"❌ Error in main thread: {e}")
    finally:
        # Make sure we always stop the bot properly
        if hasattr(bot, 'is_connected') and bot.is_connected:
            bot.stop_trading()
        print("👋 Exiting trading bot")

if __name__ == "__main__":
    main()