import asyncio
import json
import pandas as pd
from datetime import datetime, timedelta, time
from collections import defaultdict, deque
import websocket
import threading
import time as time_module
import requests
from typing import List, Dict, Optional, Tuple
import logging
import os
from dotenv import load_dotenv
import numpy as np
from dataclasses import dataclass, field
from enum import Enum
import pytz

# Windows-compatible imports for emergency close
try:
    import msvcrt  # Windows
    WINDOWS = True
except ImportError:
    WINDOWS = False

load_dotenv()

# Load Dhan credentials from environment variables
DHAN_TRADE_API_KEY = os.getenv('DHAN_TRADE_API_KEY')
DHAN_SANDBOX_CLIENT_ID = os.getenv('DHAN_SANDBOX_CLIENT_ID')
DHAN_SANDBOX_CLIENT_SECRET = os.getenv('DHAN_SANDBOX_CLIENT_SECRET')
DHAN_SANDBOX_URI = os.getenv('DHAN_SANDBOX_URI', 'https://sandbox.dhan.co/v2')
DHAN_OHLC_MIN_URI = os.getenv('DHAN_OHLC_MIN_URI', 'https://sandbox.dhan.co/v2/charts/intraday')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Indian market trading hours (IST)
IST = pytz.timezone('Asia/Kolkata')
MARKET_OPEN_TIME = time(9, 15)  # 9:15 AM IST
MARKET_CLOSE_TIME = time(15, 30)  # 3:30 PM IST

class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"

class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "SL"
    STOP_LIMIT = "SL-M"

class ExchangeCode(Enum):
    NSE = "NSE"
    BSE = "BSE"
    NFO = "NFO"  # NSE Futures & Options
    BFO = "BFO"  # BSE Futures & Options

@dataclass
class TradeExecution:
    symbol: str
    side: str
    qty: int
    order_time: datetime
    fill_time: Optional[datetime] = None
    expected_price: float = 0.0
    actual_price: float = 0.0
    slippage: float = 0.0
    execution_time_ms: float = 0.0
    order_id: str = ""
    status: str = "submitted"
    exchange_code: str = "NSE"  # Default to NSE

@dataclass
class Position:
    symbol: str
    qty: int
    side: str
    avg_price: float
    market_value: float
    unrealized_pl: float
    entry_time: datetime
    exchange_code: str = "NSE"  # Default to NSE

@dataclass
class ClosedPosition:
    symbol: str
    exit_price: float
    exit_time: datetime
    profit_cents: float
    monitoring_active: bool = True
    price_history_after_close: List[float] = field(default_factory=list)
    exchange_code: str = "NSE"  # Default to NSE

@dataclass
class PerformanceMetrics:
    total_trades: int = 0
    successful_trades: int = 0
    avg_execution_time_ms: float = 0.0
    avg_slippage: float = 0.0
    total_slippage: float = 0.0
    max_execution_time_ms: float = 0.0
    min_execution_time_ms: float = float('inf')
    executions: List[TradeExecution] = field(default_factory=list)

class DhanTradingBot:
    def __init__(self, api_key: str, client_id: str, client_secret: str, symbols: List[str],
                 use_sandbox: bool = True, profit_target_cents: float = 15.0,
                 initial_qty: int = 25, max_averaging_levels: int = 5,
                 averaging_down_threshold: float = -0.02,
                 enable_reentry: bool = True,
                 reentry_price_drop_pct: float = -0.01,  # 1% drop for re-entry
                 reentry_monitoring_time: int = 300):  # 5 minutes (300 seconds)
        self.api_key = api_key
        self.client_id = client_id
        self.client_secret = client_secret

        # Process symbols to ensure correct format for Indian markets
        self.symbols = self._format_symbols(symbols)

        self.use_sandbox = use_sandbox
        self.profit_target_cents = profit_target_cents

        # Averaging down parameters
        self.initial_qty = initial_qty
        self.max_averaging_levels = max_averaging_levels
        self.averaging_down_threshold = averaging_down_threshold
        self.current_averaging_level = 0

        # Peak price tracking parameters
        self.peak_tracking_window = 60  # Track peak over last 60 prices
        self.min_peak_profit = 0.05  # Minimum 0.05% profit to consider selling at peak
        self.recent_peaks = {}  # Symbol -> recent peak price
        self.peak_confirmation_counts = {}  # Symbol -> confirmation count
        self.required_peak_confirmations = 3  # Number of confirmations needed to confirm a peak

        # Re-entry strategy parameters
        self.enable_reentry = enable_reentry
        self.reentry_price_drop_pct = reentry_price_drop_pct
        self.reentry_monitoring_time = reentry_monitoring_time
        self.closed_positions = {}  # Symbol -> ClosedPosition
        self.reentry_timers = {}  # Symbol -> Timer

        # API endpoints
        self.api_base = DHAN_SANDBOX_URI if use_sandbox else "https://api.dhan.co/v2"
        self.ohlc_api = DHAN_OHLC_MIN_URI

        # Setup headers with authentication
        self.headers = {
            'Content-Type': 'application/json',
            'access-token': self.api_key
        }

        # Trading state
        self.positions = {}
        self.pending_orders = {}
        self.performance = PerformanceMetrics()
        self.current_prices = {}
        self.account_info = None
        self.price_history = defaultdict(list)

        # WebSocket for real-time data
        self.ws = None
        self.is_connected = False
        self.authenticated = False

        # WebSocket URL for Dhan API
        self.ws_url = f"wss://api-feed.dhan.co?version=2&token={self.api_key}&clientId={self.client_id}&authType=2"

        # Position sync timer
        self.position_sync_timer = None
        self.position_sync_interval = 30  # seconds

        # Emergency close features
        self.emergency_close_enabled = True
        self.keyboard_listener_active = False
        self.emergency_close_in_progress = False

    def _format_symbols(self, symbols: List[str]) -> List[str]:
        """Format symbols for Indian markets with exchange prefix if not present"""
        formatted_symbols = []
        for symbol in symbols:
            if ":" not in symbol:
                # Add NSE as default exchange if not specified
                formatted_symbols.append(f"NSE:{symbol}")
            else:
                formatted_symbols.append(symbol)
        return formatted_symbols

    def get_exchange_and_symbol(self, combined_symbol: str) -> Tuple[str, str]:
        """Split combined symbol into exchange and symbol parts"""
        if ":" in combined_symbol:
            exchange, symbol = combined_symbol.split(":", 1)
            return exchange, symbol
        else:
            # Default to NSE if no exchange specified
            return "NSE", combined_symbol

    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            response = requests.get(
                f'{self.api_base}/profile',
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                self.account_info = response.json()

                print("=" * 60)
                print("📋 DHAN ACCOUNT INFORMATION")
                print("=" * 60)
                print(f"Client ID: {self.account_info.get('dhanClientId', 'N/A')}")
                print(f"Token Validity: {self.account_info.get('tokenValidity', 'N/A')}")
                print(f"Active Segments: {self.account_info.get('activeSegment', 'N/A')}")
                print(f"DDPI Status: {self.account_info.get('ddpi', 'N/A')}")
                print(f"MTF Status: {self.account_info.get('mtf', 'N/A')}")
                print(f"Data Plan: {self.account_info.get('dataPlan', 'N/A')}")
                print(f"Data Validity: {self.account_info.get('dataValidity', 'N/A')}")
                print("=" * 60)

                # Get additional account information like funds
                self._get_account_funds()

                return self.account_info
            else:
                logger.error(f"Failed to get account info: {response.status_code} - {response.text}")
                return {}

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}

    def _get_account_funds(self):
        """Get account funds information"""
        try:
            response = requests.get(f'{self.api_base}/fundlimit', headers=self.headers, timeout=10)

            if response.status_code == 200:
                funds_data = response.json()

                if 'data' in funds_data:
                    funds_info = funds_data['data']

                    print("\n📊 ACCOUNT FUNDS INFORMATION")
                    print("-" * 60)
                    print(f"Cash Available: ₹{float(funds_info.get('cashAvailable', 0)):.2f}")
                    print(f"Collateral: ₹{float(funds_info.get('collateral', 0)):.2f}")
                    print(f"Margin Used: ₹{float(funds_info.get('marginUsed', 0)):.2f}")
                    print(f"Available Margin: ₹{float(funds_info.get('availableMargin', 0)):.2f}")
                    print(f"Exposure Margin: ₹{float(funds_info.get('exposureMargin', 0)):.2f}")
                    print("-" * 60)
            else:
                logger.error(f"Failed to get funds info: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error getting funds info: {e}")

    def get_positions(self) -> Dict[str, Position]:
        """Get current positions"""
        try:
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)

            if response.status_code == 200:
                positions_data = response.json()
                self.positions = {}

                # Process positions from Dhan API
                if isinstance(positions_data, dict) and 'data' in positions_data:
                    position_list = positions_data['data']

                    for pos_data in position_list:
                        # Extract symbol information
                        symbol = pos_data.get('tradingSymbol', '')
                        exchange = pos_data.get('exchange', 'NSE')
                        combined_symbol = f"{exchange}:{symbol}"

                        # Extract position details
                        qty = int(pos_data.get('netQuantity', 0))

                        # Skip positions with zero quantity
                        if qty == 0:
                            continue

                        # Determine position side
                        side = OrderSide.BUY.value if qty > 0 else OrderSide.SELL.value
                        qty = abs(qty)  # Use absolute value for quantity

                        # Extract price information
                        avg_price = float(pos_data.get('averagePrice', 0.0))
                        ltp = float(pos_data.get('lastTradedPrice', 0.0))

                        # Calculate market value and unrealized P&L
                        market_value = qty * ltp
                        unrealized_pl = (ltp - avg_price) * qty if side == OrderSide.BUY.value else (avg_price - ltp) * qty

                        # Create position object
                        position = Position(
                            symbol=combined_symbol,
                            qty=qty,
                            side=side,
                            avg_price=avg_price,
                            market_value=market_value,
                            unrealized_pl=unrealized_pl,
                            entry_time=datetime.now()  # Dhan might not provide this, using current time
                        )

                        self.positions[combined_symbol] = position

                if self.positions:
                    print("\n📊 CURRENT POSITIONS:")
                    print("-" * 60)
                    for symbol, pos in self.positions.items():
                        print(f"{symbol}: {pos.qty} shares @ ₹{pos.avg_price:.2f} | "
                              f"P&L: ₹{pos.unrealized_pl:.2f} | Value: ₹{pos.market_value:.2f}")
                    print("-" * 60)
                else:
                    print("\n📊 No open positions")

                return self.positions
            else:
                logger.error(f"Failed to get positions: {response.status_code} - {response.text}")
                return {}

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {}

    def place_market_order(self, symbol: str, qty: int, side: OrderSide) -> Optional[TradeExecution]:
        """Place a market order and track execution"""
        try:
            order_time = datetime.now()
            expected_price = self.current_prices.get(symbol, 0.0)

            # Extract exchange and symbol
            exchange, trading_symbol = self.get_exchange_and_symbol(symbol)

            # Determine the appropriate trading product
            # For Indian markets, typically using MIS (intraday) or CNC (delivery)
            trading_product = "MIS"  # Intraday position

            order_data = {
                "exchange": exchange,
                "tradingSymbol": trading_symbol,
                "quantity": qty,
                "transactionType": side.value,
                "orderType": OrderType.MARKET.value,
                "productType": trading_product,
                "validity": "DAY",
                "disclosedQuantity": 0,
                "triggerPrice": 0,
                "price": 0  # For market orders, price is 0
            }

            print(f"🔄 Placing {side.value} order: {qty} shares of {symbol} at market price")

            response = requests.post(
                f'{self.api_base}/orders',
                headers=self.headers,
                json=order_data,
                timeout=10
            )

            if response.status_code in [200, 201]:
                order_response = response.json()

                # Extract order ID from response (Dhan API returns orderId in the data field)
                if 'data' in order_response and 'orderId' in order_response['data']:
                    order_id = str(order_response['data']['orderId'])
                    print(f"✅ Order placed successfully - ID: {order_id}")

                    # Create execution tracking object
                    execution = TradeExecution(
                        symbol=symbol,
                        side=side.value,
                        qty=qty,
                        order_time=order_time,
                        expected_price=expected_price,
                        order_id=order_id,
                        exchange_code=exchange
                    )

                    # Monitor order execution
                    self.monitor_order_execution(execution)

                    return execution
                else:
                    logger.error(f"Order placed but no order ID found in response: {order_response}")
                    return None
            else:
                logger.error(f"Failed to place order: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None

    def monitor_order_execution(self, execution: TradeExecution):
        """Monitor order execution and calculate metrics"""
        max_wait_time = 30  # seconds
        check_interval = 0.5  # seconds
        waited = 0

        while waited < max_wait_time:
            try:
                response = requests.get(
                    f'{self.api_base}/orders/{execution.order_id}',
                    headers=self.headers,
                    timeout=5
                )

                if response.status_code == 200:
                    order_data = response.json()

                    # Extract status from response (Dhan API specific)
                    if 'data' in order_data:
                        order_details = order_data['data']
                        status = order_details.get('status', '').upper()

                        # Map Dhan order status to our internal status
                        if status in ['COMPLETED', 'TRADED', 'FILLED']:
                            execution.fill_time = datetime.now()
                            execution.actual_price = float(order_details.get('averagePrice', 0.0))
                            execution.execution_time_ms = (execution.fill_time - execution.order_time).total_seconds() * 1000
                            execution.status = 'filled'

                            # Calculate slippage
                            if execution.side == OrderSide.BUY.value:
                                execution.slippage = execution.actual_price - execution.expected_price
                            else:  # sell
                                execution.slippage = execution.expected_price - execution.actual_price

                            print(f"✅ Order FILLED: {execution.symbol} @ ₹{execution.actual_price:.2f}")
                            print(f"   Execution Time: {execution.execution_time_ms:.1f}ms")
                            print(f"   Slippage: ₹{execution.slippage:.4f}")

                            # Update performance metrics
                            self.update_performance_metrics(execution)

                            return execution

                        elif status in ['CANCELLED', 'REJECTED', 'EXPIRED']:
                            execution.status = status.lower()
                            print(f"❌ Order {status}: {execution.symbol}")
                            return execution

                        # If still pending, continue waiting
                        elif status in ['PENDING', 'OPEN', 'PARTIALLY_FILLED', 'TRIGGER_PENDING']:
                            pass

                time_module.sleep(check_interval)
                waited += check_interval

            except Exception as e:
                logger.error(f"Error monitoring order: {e}")
                break

        print(f"⏰ Order monitoring timeout for {execution.symbol}")
        execution.status = 'timeout'
        return execution

    def update_performance_metrics(self, execution: TradeExecution):
        """Update overall performance metrics"""
        self.performance.executions.append(execution)
        self.performance.total_trades += 1

        if execution.status == 'filled':
            self.performance.successful_trades += 1
            self.performance.total_slippage += abs(execution.slippage)

            # Update execution time metrics
            exec_time = execution.execution_time_ms
            self.performance.max_execution_time_ms = max(self.performance.max_execution_time_ms, exec_time)
            self.performance.min_execution_time_ms = min(self.performance.min_execution_time_ms, exec_time)

            # Calculate averages
            filled_executions = [e for e in self.performance.executions if e.status == 'filled']
            if filled_executions:
                self.performance.avg_execution_time_ms = np.mean([e.execution_time_ms for e in filled_executions])
                self.performance.avg_slippage = np.mean([abs(e.slippage) for e in filled_executions])

    def should_average_down(self, symbol: str) -> bool:
        """Determine if we should average down on a position"""
        # Check if we have a position for this symbol
        if symbol not in self.positions:
            return False

        position = self.positions[symbol]
        current_price = self.current_prices.get(symbol, 0)

        # Calculate unrealized P&L percentage
        if position.avg_price > 0 and current_price > 0:
            if position.side == 'long':
                pnl_pct = (current_price - position.avg_price) / position.avg_price
            else:  # short position
                pnl_pct = (position.avg_price - current_price) / position.avg_price

            # Scale the threshold based on averaging level
            # For level 1, use the base threshold
            # For higher levels, scale it (e.g., level 10 would be -10%)
            scaled_threshold = self.averaging_down_threshold
            if self.current_averaging_level > 0:
                # Scale threshold based on level (e.g., level 10 = -10%)
                scaled_threshold = -0.01 * (self.current_averaging_level + 1)

            # Check if we're at a loss beyond the scaled threshold and haven't exceeded max levels
            if (pnl_pct <= scaled_threshold and
                self.current_averaging_level < self.max_averaging_levels):
                print(f"Averaging down threshold for level {self.current_averaging_level+1}: {scaled_threshold:.2%}")
                return True

        return False

    def is_at_peak(self, symbol: str, current_price: float) -> bool:
        """Determine if current price is at a local peak"""
        if len(self.price_history[symbol]) < self.peak_tracking_window:
            return False

        recent_prices = self.price_history[symbol][-self.peak_tracking_window:]
        recent_max = max(recent_prices)

        # Initialize peak tracking for this symbol if not already done
        if symbol not in self.recent_peaks:
            self.recent_peaks[symbol] = current_price
            self.peak_confirmation_counts[symbol] = 0

        # Update recent peak
        if current_price >= recent_max:
            self.recent_peaks[symbol] = current_price
            self.peak_confirmation_counts[symbol] = 0
            return False

        # Check if price is declining from peak
        if current_price < self.recent_peaks[symbol]:
            self.peak_confirmation_counts[symbol] += 1
            if self.peak_confirmation_counts[symbol] >= self.required_peak_confirmations:
                print(f"📉 Peak detected for {symbol}: ₹{self.recent_peaks[symbol]:.4f} -> ₹{current_price:.4f}")
                return True

        return False

    def check_exit_conditions(self, symbol: str) -> Tuple[bool, float]:
        """Check if exit conditions are met for a position
        Returns a tuple of (should_exit, profit_cents)
        """
        if symbol not in self.positions:
            return False, 0.0

        position = self.positions[symbol]
        current_price = self.current_prices.get(symbol, 0)

        if position.avg_price > 0 and current_price > 0:
            if position.side == 'long':
                # For Indian markets, convert to paise (1/100th of a rupee)
                profit_cents = (current_price - position.avg_price) * 100  # Convert to paise
                pnl_pct = (current_price - position.avg_price) / position.avg_price
            else:  # short position
                profit_cents = (position.avg_price - current_price) * 100  # Convert to paise
                pnl_pct = (position.avg_price - current_price) / position.avg_price

            # Exit if profit target in paise is reached
            if profit_cents >= self.profit_target_cents:
                print(f"🎯 Profit target reached for {symbol}: ₹{profit_cents/100:.2f} (₹{current_price:.2f} - ₹{position.avg_price:.2f})")
                return True, profit_cents

            # Exit if we're at a peak with minimum profit
            if pnl_pct >= self.min_peak_profit and self.is_at_peak(symbol, current_price):
                print(f"📈 Taking profit at peak for {symbol}: {pnl_pct:.2%} (₹{profit_cents/100:.2f})")
                return True, profit_cents

        return False, 0.0

    def track_closed_position(self, symbol: str, exit_price: float, profit_cents: float):
        """Track a closed position for potential re-entry"""
        if not self.enable_reentry:
            return

        # Create a new closed position entry
        exchange, trading_symbol = self.get_exchange_and_symbol(symbol)

        closed_position = ClosedPosition(
            symbol=symbol,
            exit_price=exit_price,
            exit_time=datetime.now(),
            profit_cents=profit_cents,
            exchange_code=exchange
        )

        self.closed_positions[symbol] = closed_position
        print(f"🔍 Tracking {symbol} for re-entry opportunity after closing at ₹{exit_price:.2f}")

        # Start a timer to stop monitoring after the specified time
        self.reentry_timers[symbol] = threading.Timer(
            self.reentry_monitoring_time,
            self.stop_reentry_monitoring,
            args=[symbol]
        )
        self.reentry_timers[symbol].daemon = True
        self.reentry_timers[symbol].start()

    def stop_reentry_monitoring(self, symbol: str):
        """Stop monitoring a closed position for re-entry"""
        if symbol in self.closed_positions:
            self.closed_positions[symbol].monitoring_active = False
            print(f"⏱️ Stopped monitoring {symbol} for re-entry (timeout reached)")

            # Clean up after some time to avoid memory leaks
            def remove_closed_position():
                if symbol in self.closed_positions:
                    del self.closed_positions[symbol]
                if symbol in self.reentry_timers:
                    del self.reentry_timers[symbol]

            cleanup_timer = threading.Timer(60, remove_closed_position)
            cleanup_timer.daemon = True
            cleanup_timer.start()

    def execute_trading_strategy(self, symbol: str, current_price: float):
        """Execute the trading strategy for a symbol"""
        # Check if market is open
        if not self.is_market_open():
            return

        # Update current price
        self.current_prices[symbol] = current_price

        # Check for re-entry opportunities for closed positions
        self.check_reentry_opportunities(symbol, current_price)

        # Check if we have a position for this symbol
        if symbol in self.positions:
            position = self.positions[symbol]

            # Update position's market value and unrealized P&L
            if position.side == 'long':
                position.market_value = position.qty * current_price
                position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
            else:  # short position
                position.market_value = position.qty * current_price
                position.unrealized_pl = (position.qty * position.avg_price) - position.market_value

            # Check if we should exit the position
            should_exit, profit_cents = self.check_exit_conditions(symbol)
            if should_exit:
                # Place order to close position
                close_side = OrderSide.SELL if position.side == 'long' else OrderSide.BUY
                execution = self.place_market_order(symbol, position.qty, close_side)

                if execution and execution.status == 'filled':
                    # Track the closed position for potential re-entry
                    self.track_closed_position(symbol, current_price, profit_cents)

                del self.positions[symbol]
                self.current_averaging_level = 0  # Reset averaging level
                return

            # Check if we should average down
            if self.should_average_down(symbol):
                self.current_averaging_level += 1
                avg_qty = self.calculate_averaging_quantity()

                print(f"📉 Averaging down on {symbol} (Level {self.current_averaging_level})")
                print(f"   Current Price: ₹{current_price:.2f} | Avg Price: ₹{position.avg_price:.2f}")
                print(f"   P&L: ₹{position.unrealized_pl:.2f} | Adding {avg_qty} shares")

                # Place order for averaging down
                avg_side = OrderSide.BUY if position.side == 'long' else OrderSide.SELL
                execution = self.place_market_order(symbol, avg_qty, avg_side)

                if execution and execution.status == 'filled':
                    # Update position with new average price and quantity
                    new_qty = position.qty + avg_qty
                    new_avg_price = ((position.qty * position.avg_price) +
                                    (avg_qty * execution.actual_price)) / new_qty

                    position.qty = new_qty
                    position.avg_price = new_avg_price
                    position.market_value = position.qty * current_price

                    # Recalculate unrealized P&L
                    if position.side == 'long':
                        position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                    else:  # short position
                        position.unrealized_pl = (position.qty * position.avg_price) - position.market_value

    def check_reentry_opportunities(self, symbol: str, current_price: float):
        """Check for re-entry opportunities for closed positions"""
        if not self.enable_reentry or symbol not in self.closed_positions:
            return

        closed_position = self.closed_positions[symbol]

        # Skip if monitoring is no longer active
        if not closed_position.monitoring_active:
            return

        # Add current price to history
        closed_position.price_history_after_close.append(current_price)

        # Calculate price drop percentage since closing
        price_change_pct = (current_price - closed_position.exit_price) / closed_position.exit_price

        # Check if price has dropped enough for re-entry
        if price_change_pct <= self.reentry_price_drop_pct:
            print(f"🔄 Re-entry opportunity detected for {symbol}")
            print(f"   Exit Price: ₹{closed_position.exit_price:.2f} | Current Price: ₹{current_price:.2f}")
            print(f"   Price Drop: {price_change_pct:.2%} | Target: {self.reentry_price_drop_pct:.2%}")

            # Place order to re-enter position
            qty = self.initial_qty  # Start with initial quantity
            side = OrderSide.BUY  # Always re-enter with a long position for now

            execution = self.place_market_order(symbol, qty, side)
            if execution and execution.status == 'filled':
                print(f"✅ Re-entered {symbol} at ₹{execution.actual_price:.2f} with {qty} shares")

                # Stop monitoring this closed position
                closed_position.monitoring_active = False

                # Clean up
                if symbol in self.reentry_timers and self.reentry_timers[symbol].is_alive():
                    self.reentry_timers[symbol].cancel()

                # Remove from closed positions after a delay
                def remove_closed_position():
                    if symbol in self.closed_positions:
                        del self.closed_positions[symbol]
                    if symbol in self.reentry_timers:
                        del self.reentry_timers[symbol]

                cleanup_timer = threading.Timer(5, remove_closed_position)
                cleanup_timer.daemon = True
                cleanup_timer.start()

        # If no position exists for this symbol, open a new position
        elif symbol not in self.positions and self.is_market_open():
            # Place a new buy order with initial quantity
            print(f"🔵 Opening new position for {symbol} at ₹{current_price:.2f}")
            print(f"   Quantity: {self.initial_qty} shares")

            # Place order to open position (long side)
            execution = self.place_market_order(symbol, self.initial_qty, OrderSide.BUY)

            if execution and execution.status == 'filled':
                exchange, trading_symbol = self.get_exchange_and_symbol(symbol)

                # Create new position
                position = Position(
                    symbol=symbol,
                    qty=self.initial_qty,
                    side='long',
                    avg_price=execution.actual_price,
                    market_value=self.initial_qty * current_price,
                    unrealized_pl=0.0,
                    entry_time=datetime.now(),
                    exchange_code=exchange
                )

                # Add to positions dictionary
                self.positions[symbol] = position
                self.current_averaging_level = 0  # Reset averaging level

                print(f"✅ New position opened: {position.qty} shares of {symbol} @ ₹{position.avg_price:.2f}")

    def is_market_open(self) -> bool:
        """Check if the Indian market is currently open"""
        now = datetime.now(IST)
        current_time = now.time()

        # Check if it's a weekday (0 = Monday, 6 = Sunday)
        if now.weekday() >= 5:  # Saturday or Sunday
            return False

        # Check if current time is within market hours
        if current_time >= MARKET_OPEN_TIME and current_time <= MARKET_CLOSE_TIME:
            return True

        return False

    def emergency_close_all_positions(self):
        """Emergency close all positions"""
        if self.emergency_close_in_progress:
            print("⚠️ Emergency close already in progress...")
            return

        self.emergency_close_in_progress = True
        print("\n🚨 EMERGENCY CLOSE INITIATED - CLOSING ALL POSITIONS 🚨")

        try:
            # Get current positions from Dhan
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)

            if response.status_code == 200:
                positions_data = response.json()

                # Extract position list based on Dhan API structure
                position_list = []
                if isinstance(positions_data, dict) and 'data' in positions_data:
                    position_list = positions_data['data']

                if not position_list:
                    print("No positions to close")
                    self.emergency_close_in_progress = False
                    return

                print(f"Closing {len(position_list)} positions...")

                total_pnl = 0.0

                for pos in position_list:
                    # Extract position details
                    symbol = pos.get('tradingSymbol', '')
                    exchange = pos.get('exchange', 'NSE')
                    combined_symbol = f"{exchange}:{symbol}"

                    qty = int(pos.get('netQuantity', 0))

                    # Skip positions with zero quantity
                    if qty == 0:
                        continue

                    # Determine closing side
                    side = OrderSide.SELL if qty > 0 else OrderSide.BUY
                    qty = abs(qty)  # Ensure quantity is positive

                    unrealized_pl = float(pos.get('unrealizedPnl', 0.0))

                    print(f"Closing {qty} shares of {combined_symbol} ({side.value})")

                    # Place market order to close position
                    execution = self.place_market_order(combined_symbol, qty, side)

                    if execution and execution.status == 'filled':
                        total_pnl += unrealized_pl

                        # Remove from our internal positions tracking
                        if combined_symbol in self.positions:
                            del self.positions[combined_symbol]

                print(f"\n✅ Emergency close complete. Total P&L: ₹{total_pnl:.2f}")
            else:
                print(f"Failed to get positions: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error during emergency close: {e}")
            print(f"Error during emergency close: {e}")

        finally:
            self.emergency_close_in_progress = False

    def display_position_summary(self):
        """Display a summary of current positions"""
        try:
            # Get fresh position data from Dhan
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)

            # Also display closed positions being monitored for re-entry
            if self.enable_reentry and self.closed_positions:
                print("\n📊 Closed Positions Being Monitored for Re-entry:")
                print("-" * 80)
                print(f"{'Symbol':<10} {'Exit Price':>10} {'Current':>10} {'Change %':>10} {'Target %':>10} {'Time Left':>15}")
                print("-" * 80)

                current_time = datetime.now()

                for symbol, closed_pos in self.closed_positions.items():
                    if not closed_pos.monitoring_active:
                        continue

                    current_price = self.current_prices.get(symbol, 0)
                    if current_price > 0:
                        price_change_pct = (current_price - closed_pos.exit_price) / closed_pos.exit_price * 100

                        # Calculate time left for monitoring
                        elapsed_seconds = (current_time - closed_pos.exit_time).total_seconds()
                        remaining_seconds = max(0, self.reentry_monitoring_time - elapsed_seconds)
                        time_left = f"{int(remaining_seconds // 60)}m {int(remaining_seconds % 60)}s"

                        print(f"{symbol:<10} ₹{closed_pos.exit_price:>9.2f} ₹{current_price:>9.2f} {price_change_pct:>9.2f}% {self.reentry_price_drop_pct*100:>9.2f}% {time_left:>15}")

                print("-" * 80)

            if response.status_code == 200:
                positions_data = response.json()

                # Extract position list based on Dhan API structure
                position_list = []
                if isinstance(positions_data, dict) and 'data' in positions_data:
                    position_list = positions_data['data']

                if not position_list:
                    print("\n📊 No open positions")
                    return

                print("\n" + "=" * 80)
                print("📊 POSITION SUMMARY")
                print("=" * 80)
                print(f"{'Symbol':<15} {'Exchange':<8} {'Side':<6} {'Quantity':<10} {'Avg Price':<12} {'Current':<12} {'Market Value':<15} {'Unrealized P&L':<15} {'P&L %':<10}")
                print("-" * 80)

                total_market_value = 0.0
                total_unrealized_pl = 0.0

                for pos in position_list:
                    # Extract position details
                    symbol = pos.get('tradingSymbol', '')
                    exchange = pos.get('exchange', 'NSE')

                    qty = int(pos.get('netQuantity', 0))

                    # Skip positions with zero quantity
                    if qty == 0:
                        continue

                    # Determine position side
                    side = 'long' if qty > 0 else 'short'
                    qty = abs(qty)  # Ensure quantity is positive

                    avg_price = float(pos.get('averagePrice', 0.0))
                    current_price = float(pos.get('lastPrice', 0.0))
                    market_value = qty * current_price
                    unrealized_pl = float(pos.get('unrealizedPnl', 0.0))

                    # Calculate P&L percentage
                    if avg_price > 0:
                        if side == 'long':
                            pnl_pct = (current_price - avg_price) / avg_price
                        else:  # short
                            pnl_pct = (avg_price - current_price) / avg_price
                    else:
                        pnl_pct = 0.0

                    # Format the row
                    print(f"{symbol:<15} {exchange:<8} {side:<6} {qty:<10} ₹{avg_price:<11.2f} ₹{current_price:<11.2f} ₹{market_value:<14.2f} ₹{unrealized_pl:<14.2f} {pnl_pct:<9.2%}")

                    total_market_value += market_value
                    total_unrealized_pl += unrealized_pl

                print("-" * 80)
                print(f"{'TOTAL':<40} ₹{total_market_value:<14.2f} ₹{total_unrealized_pl:<14.2f}")
                print("=" * 80)
            else:
                print(f"Failed to get positions: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error displaying position summary: {e}")
            print(f"Error displaying position summary: {e}")

    def keyboard_listener(self):
        """Listen for keyboard commands"""
        if os.name == 'nt':  # Windows
            self.windows_keyboard_listener()
        else:  # Unix/Linux/Mac
            self.unix_keyboard_listener()

    def windows_keyboard_listener(self):
        """Windows-specific keyboard listener using msvcrt"""
        import msvcrt

        print("\n⌨️  Keyboard controls active:")
        print("   'q' - Emergency close all positions")
        print("   'p' - Display position summary")
        print("   's' - Stop trading")
        print("   'a' - Add to position")

        if self.enable_reentry:
            print("\n🔄 Re-entry feature is active:")
            print(f"   - After taking profit, positions will be monitored for {self.reentry_monitoring_time} seconds")
            print(f"   - Re-entry will trigger if price drops by {abs(self.reentry_price_drop_pct)*100:.1f}% from exit price")

        self.keyboard_listener_active = True

        while self.keyboard_listener_active:
            if msvcrt.kbhit():
                key = msvcrt.getch().decode('utf-8').lower()

                if key == 'q':
                    print("\n🚨 Emergency close triggered by keyboard")
                    self.emergency_close_all_positions()
                elif key == 'p':
                    self.display_position_summary()
                elif key == 's':
                    print("\n🛑 Stopping trading...")
                    self.stop_trading()
                elif key == 'a':
                    # Clear any remaining characters in the buffer before handling input
                    while msvcrt.kbhit():
                        msvcrt.getch()
                    self.handle_add_position_input()
                    # Clear any characters that might have been pressed during input
                    while msvcrt.kbhit():
                        msvcrt.getch()
                    print("\n⌨️  Keyboard controls active again. Press 'a' to add another position.")

            time_module.sleep(0.1)  # Prevent high CPU usage

    def unix_keyboard_listener(self):
        """Unix-specific keyboard listener using sys and tty"""
        import sys
        import tty
        import termios

        print("\n⌨️  Keyboard controls active:")
        print("   'q' - Emergency close all positions")
        print("   'p' - Display position summary")
        print("   's' - Stop trading")
        print("   'a' - Add to position")

        if self.enable_reentry:
            print("\n🔄 Re-entry feature is active:")
            print(f"   - After taking profit, positions will be monitored for {self.reentry_monitoring_time} seconds")
            print(f"   - Re-entry will trigger if price drops by {abs(self.reentry_price_drop_pct)*100:.1f}% from exit price")

        self.keyboard_listener_active = True
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)

        try:
            tty.setraw(fd)
            while self.keyboard_listener_active:
                key = sys.stdin.read(1).lower()

                if key == 'q':
                    print("\n🚨 Emergency close triggered by keyboard")
                    self.emergency_close_all_positions()
                elif key == 'p':
                    self.display_position_summary()
                elif key == 's':
                    print("\n🛑 Stopping trading...")
                    self.stop_trading()
                elif key == 'a':
                    # Need to restore terminal settings before getting input
                    termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
                    self.handle_add_position_input()
                    # Set back to raw mode
                    tty.setraw(fd)

                time_module.sleep(0.1)  # Prevent high CPU usage
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)

    def start_keyboard_listener(self):
        """Start keyboard listener in a separate thread"""
        keyboard_thread = threading.Thread(target=self.keyboard_listener)
        keyboard_thread.daemon = True  # Thread will exit when main program exits
        keyboard_thread.start()

    def stop_keyboard_listener(self):
        """Stop the keyboard listener"""
        self.keyboard_listener_active = False

    def calculate_averaging_quantity(self) -> int:
        """Calculate quantity for averaging down"""
        return self.initial_qty * (2 ** self.current_averaging_level)

    def on_message(self, ws, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)

            # Process Dhan WebSocket message format
            # Note: This implementation will need to be adjusted based on actual Dhan WebSocket API

            # Example message processing logic - adapt to actual Dhan WebSocket format
            if 'type' in data and data['type'] == 'trade':
                symbol = data.get('symbol', '')
                exchange = data.get('exchange', 'NSE')
                combined_symbol = f"{exchange}:{symbol}"

                price = float(data.get('price', 0))

                if combined_symbol and price > 0:
                    # Update current price
                    self.current_prices[combined_symbol] = price

                    # Store price history (limited to last 100 prices)
                    self.price_history[combined_symbol].append(price)
                    if len(self.price_history[combined_symbol]) > 100:
                        self.price_history[combined_symbol].pop(0)

                    # Display real-time price updates and PnL calculations
                    if combined_symbol in self.positions:
                        position = self.positions[combined_symbol]
                        # Update position's market value and unrealized P&L
                        if position.side == 'long':
                            position.market_value = position.qty * price
                            position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                            pnl_pct = (price - position.avg_price) / position.avg_price * 100
                        else:  # short position
                            position.market_value = position.qty * price
                            position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
                            pnl_pct = (position.avg_price - price) / position.avg_price * 100

                        # Display price update with PnL information
                        print(f"💹 {combined_symbol}: ₹{price:.4f} (₹{position.unrealized_pl:.2f} | {pnl_pct:.2f}%)", end="\r")
                    else:
                        # Just display price update if no position
                        print(f"💹 {combined_symbol}: ₹{price:.4f}", end="\r")

                    # Execute trading strategy
                    self.execute_trading_strategy(combined_symbol, price)

        except json.JSONDecodeError:
            logger.error(f"Failed to parse WebSocket message: {message}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")

    def authenticate_ws(self):
        """Authenticate with Dhan WebSocket"""
        auth_data = {
            "action": "auth",
            "key": self.client_id,
            "secret": self.api_key
        }
        self.ws.send(json.dumps(auth_data))
        print("🔐 Authenticating WebSocket connection...")

    def subscribe_to_trades(self):
        """Subscribe to trade updates for our symbols"""
        symbols_data = []
        for symbol in self.symbols:
            exchange, trading_symbol = self.get_exchange_and_symbol(symbol)
            symbols_data.append({
                "exchange": exchange,
                "token": self._get_instrument_token(exchange, trading_symbol),
                "symbol": trading_symbol
            })

        sub_data = {
            "action": "subscribe",
            "params": {
                "mode": "full",
                "symbols": symbols_data
            }
        }
        self.ws.send(json.dumps(sub_data))
        print(f"📊 Subscribing to trade data for: {', '.join(self.symbols)}")

    def _get_instrument_token(self, exchange, symbol):
        """Get instrument token for a symbol (required for WebSocket subscription)"""
        try:
            # This is a placeholder - Dhan might have an API to get instrument tokens
            # You'll need to implement this based on Dhan's API documentation
            response = requests.get(
                f'{self.api_base}/instruments?exchange={exchange}&symbol={symbol}',
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if 'data' in data and len(data['data']) > 0:
                    return data['data'][0].get('instrumentToken', '')

            # If we can't get the token, return an empty string
            return ""
        except Exception as e:
            logger.error(f"Error getting instrument token: {e}")
            return ""

    def on_error(self, ws, error):
        """Handle WebSocket errors"""
        logger.error(f"WebSocket error: {error}")
        print(f"❌ WebSocket error: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket connection close"""
        self.is_connected = False
        self.authenticated = False
        logger.info("WebSocket connection closed")
        print("📴 WebSocket connection closed")

    def on_open(self, ws):
        """Handle WebSocket connection open"""
        self.is_connected = True
        logger.info("WebSocket connection established")
        print("📲 WebSocket connection established")
        self.authenticate_ws()

    def sync_positions_with_dhan(self):
        """Sync internal position tracking with actual positions from Dhan API"""
        try:
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)

            if response.status_code == 200:
                positions_data = response.json()

                # Extract position list based on Dhan API structure
                position_list = []
                if isinstance(positions_data, dict) and 'data' in positions_data:
                    position_list = positions_data['data']

                dhan_positions = {}

                # Process positions from Dhan API
                for pos_data in position_list:
                    # Extract symbol information
                    symbol = pos_data.get('tradingSymbol', '')
                    exchange = pos_data.get('exchange', 'NSE')
                    combined_symbol = f"{exchange}:{symbol}"

                    # Extract position details
                    qty = int(pos_data.get('netQuantity', 0))

                    # Skip positions with zero quantity
                    if qty == 0:
                        continue

                    # Determine position side
                    side = 'long' if qty > 0 else 'short'
                    qty = abs(qty)  # Ensure quantity is positive

                    # Get average price
                    avg_price = float(pos_data.get('averagePrice', 0.0))

                    # Calculate market value and unrealized P&L
                    current_price = float(pos_data.get('lastPrice', 0.0))
                    self.current_prices[combined_symbol] = current_price

                    market_value = qty * current_price
                    unrealized_pl = float(pos_data.get('unrealizedPnl', 0.0))

                    position = Position(
                        symbol=combined_symbol,
                        qty=qty,
                        side=side,
                        avg_price=avg_price,
                        market_value=market_value,
                        unrealized_pl=unrealized_pl,
                        entry_time=datetime.now(),  # Dhan doesn't provide entry time in positions
                        exchange_code=exchange
                    )
                    dhan_positions[combined_symbol] = position

                # Check for new positions or updated positions
                for symbol, position in dhan_positions.items():
                    if symbol not in self.positions:
                        # New position detected (likely manually opened)
                        self.positions[symbol] = position
                        print(f"\n🔍 Detected manually opened position: {symbol}")
                        print(f"   {position.qty} shares @ ₹{position.avg_price:.2f} | Side: {position.side}")
                    else:
                        # Position exists, check if quantity or average price changed
                        existing_pos = self.positions[symbol]
                        if existing_pos.qty != position.qty or abs(existing_pos.avg_price - position.avg_price) > 0.01:
                            # Position was modified manually
                            old_qty = existing_pos.qty
                            old_avg = existing_pos.avg_price
                            self.positions[symbol] = position
                            print(f"\n🔄 Position updated for {symbol}:")
                            print(f"   Old: {old_qty} shares @ ₹{old_avg:.2f}")
                            print(f"   New: {position.qty} shares @ ₹{position.avg_price:.2f}")

                # Check for closed positions
                closed_symbols = [s for s in self.positions if s not in dhan_positions]
                for symbol in closed_symbols:
                    print(f"\n❌ Position closed for {symbol} (not found in Dhan)")
                    del self.positions[symbol]

                return True
            else:
                logger.error(f"Failed to sync positions: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error syncing positions: {e}")
            return False

    def handle_add_position_input(self):
        """Get user input for adding to a position and execute the order"""
        print("\n📈 Add to Position")

        # Display current positions first
        self.display_position_summary()

        # If we have only one position, use that symbol automatically
        if len(self.positions) == 1:
            symbol = list(self.positions.keys())[0]
            print(f"\nUsing current position: {symbol}")
        # If we have multiple positions, ask which one to add to
        elif len(self.positions) > 1:
            position_symbols = list(self.positions.keys())
            for i, sym in enumerate(position_symbols):
                print(f"{i+1}. {sym} - {self.positions[sym].qty} shares @ ₹{self.positions[sym].avg_price:.2f}")

            try:
                choice = input("\nSelect position number to add to (or press Enter to cancel): ").strip()
                if not choice:
                    print("Operation cancelled.")
                    return

                choice_idx = int(choice) - 1
                if choice_idx < 0 or choice_idx >= len(position_symbols):
                    print("Invalid selection.")
                    return

                symbol = position_symbols[choice_idx]
            except ValueError:
                print("Invalid selection. Please enter a number.")
                return
        # If no positions, ask for symbol
        else:
            symbol_input = input("\nEnter symbol (e.g., NSE:RELIANCE or just RELIANCE for NSE, press Enter to cancel): ").strip().upper()
            if not symbol_input:
                print("Operation cancelled.")
                return

            # Format symbol if needed
            if ":" not in symbol_input:
                symbol = f"NSE:{symbol_input}"  # Default to NSE
            else:
                symbol = symbol_input

            # Check if symbol is in tracked symbols
            if symbol not in self.symbols:
                add_to_tracked = input(f"{symbol} is not in tracked symbols. Add it? (y/n): ").strip().lower()
                if add_to_tracked == 'y':
                    self.symbols.append(symbol)
                    print(f"Added {symbol} to tracked symbols")
                    # Subscribe to trades for this symbol
                    if self.is_connected and self.authenticated:
                        exchange, trading_symbol = self.get_exchange_and_symbol(symbol)
                        sub_data = {
                            "type": "subscribe",
                            "symbols": [{
                                "exchange": exchange,
                                "symbol": trading_symbol
                            }]
                        }
                        self.ws.send(json.dumps(sub_data))
                        print(f"Subscribed to trade data for {symbol}")
                else:
                    print("Operation cancelled.")
                    return

        # Check if market is open
        if not self.is_market_open():
            print("❌ Market is currently closed. Cannot add to position.")
            return

        # Calculate suggested quantity (2x current position or initial_qty if no position)
        suggested_qty = self.initial_qty
        if symbol in self.positions:
            suggested_qty = self.positions[symbol].qty * 2

        # Get quantity input with suggested value
        try:
            qty_input = input(f"Enter quantity to add to {symbol} (suggested: {suggested_qty}, press Enter to use suggested): ").strip()
            if not qty_input:
                additional_qty = suggested_qty
                print(f"Using suggested quantity: {additional_qty}")
            else:
                additional_qty = int(qty_input)

            if additional_qty <= 0:
                print("Quantity must be positive.")
                return
        except ValueError:
            print("Invalid quantity. Please enter a number.")
            return

        # Confirm the operation
        try:
            current_price = self.current_prices.get(symbol)
            if not current_price or current_price <= 0:
                print(f"Could not get current price for {symbol}. Waiting for price data...")
                # Wait a bit for price data
                for _ in range(5):  # Try for 5 seconds
                    time_module.sleep(1)
                    current_price = self.current_prices.get(symbol)
                    if current_price and current_price > 0:
                        break

                if not current_price or current_price <= 0:
                    print(f"Still no price data for {symbol}. Cannot proceed.")
                    return

            estimated_cost = current_price * additional_qty
            confirm = input(f"Add {additional_qty} shares of {symbol} at ~₹{current_price:.2f} (est. ₹{estimated_cost:.2f})? (y/n): ").strip().lower()

            if confirm != 'y':
                print("Operation cancelled.")
                return

            # Execute the order
            success = self.add_to_position(symbol, additional_qty, force_sync=True)

            if success:
                print(f"✅ Successfully added {additional_qty} shares of {symbol}")
            else:
                print(f"❌ Failed to add to position for {symbol}")

        except Exception as e:
            print(f"Error: {e}")

    def add_to_position(self, symbol: str, additional_qty: int, force_sync: bool = True):
        """Manually add to an existing position at current market price

        Args:
            symbol: The stock symbol to add to
            additional_qty: Number of shares to add
            force_sync: Whether to force an immediate sync with Dhan after the order

        Returns:
            bool: True if successful, False otherwise
        """
        if symbol not in self.symbols:
            print(f"❌ Error: {symbol} is not in the list of tracked symbols: {', '.join(self.symbols)}")
            return False

        if additional_qty <= 0:
            print("❌ Error: Additional quantity must be positive")
            return False

        current_price = self.current_prices.get(symbol)
        if not current_price or current_price <= 0:
            print(f"❌ Error: No valid current price for {symbol}. Please wait for price data.")
            return False

        # Check if market is open
        if not self.is_market_open():
            print("❌ Error: Market is currently closed")
            return False

        # Check if we already have a position for this symbol
        position_exists = symbol in self.positions
        position_side = 'long' if position_exists else None
        if position_exists:
            position_side = self.positions[symbol].side
            print(f"\n🔍 Adding to existing {position_side} position for {symbol}")
            print(f"   Current: {self.positions[symbol].qty} shares @ ₹{self.positions[symbol].avg_price:.2f}")
            print(f"   Adding: {additional_qty} shares @ ~₹{current_price:.2f} (market price)")
        else:
            print(f"\n🔍 Opening new position for {symbol}")
            print(f"   Quantity: {additional_qty} shares @ ~₹{current_price:.2f} (market price)")
            position_side = 'long'  # Default to long for new positions

        # Place the order
        side = OrderSide.BUY if position_side == 'long' else OrderSide.SELL
        execution = self.place_market_order(symbol, additional_qty, side)

        if execution and execution.status == 'filled':
            # If this was a new position, it will be created by sync_positions_with_dhan
            # If it was an existing position, we'll update our local tracking immediately
            if position_exists:
                position = self.positions[symbol]
                old_qty = position.qty
                old_avg_price = position.avg_price
                new_qty = position.qty + additional_qty

                # Calculate new average price
                new_avg_price = ((old_qty * old_avg_price) +
                               (additional_qty * execution.actual_price)) / new_qty

                # Update the position
                position.qty = new_qty
                position.avg_price = new_avg_price
                position.market_value = position.qty * current_price

                # Recalculate unrealized P&L
                if position.side == 'long':
                    position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                    position.pnl_percentage = (current_price - position.avg_price) / position.avg_price
                else:  # short position
                    position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
                    position.pnl_percentage = (position.avg_price - current_price) / position.avg_price

                print(f"✅ Position updated: {position.qty} shares @ ₹{position.avg_price:.2f}")
                print(f"   Old: {old_qty} shares @ ₹{old_avg_price:.2f}")
                print(f"   New P&L: ₹{position.unrealized_pl:.2f} ({position.pnl_percentage:.2%})")
            else:
                # Create a new position object immediately
                exchange, trading_symbol = self.get_exchange_and_symbol(symbol)

                new_position = Position(
                    symbol=symbol,
                    qty=additional_qty,
                    side=position_side,
                    avg_price=execution.actual_price,
                    market_value=additional_qty * current_price,
                    unrealized_pl=0.0,  # Will be calculated below
                    entry_time=datetime.now(),
                    exchange_code=exchange
                )

                # Calculate initial P&L
                if position_side == 'long':
                    new_position.unrealized_pl = (current_price - execution.actual_price) * additional_qty
                    new_position.pnl_percentage = (current_price - execution.actual_price) / execution.actual_price
                else:  # short
                    new_position.unrealized_pl = (execution.actual_price - current_price) * additional_qty
                    new_position.pnl_percentage = (execution.actual_price - current_price) / execution.actual_price

                # Add to our positions dictionary
                self.positions[symbol] = new_position
                print(f"✅ New position created: {additional_qty} shares @ ₹{execution.actual_price:.2f}")

            # Force a sync with Dhan if requested
            if force_sync:
                print("🔄 Forcing position sync with Dhan...")
                self.sync_positions_with_dhan()

            return True
        else:
            print(f"❌ Failed to add to position: {execution.status if execution else 'Unknown error'}")
            return False

    def start_position_sync_timer(self):
        """Start a timer to periodically sync positions with Dhan API"""
        # Cancel any existing timer
        if self.position_sync_timer:
            self.position_sync_timer.cancel()

        # Define a function to run the sync and schedule the next run
        def sync_and_reschedule():
            print(f"\n🔄 Syncing positions with Dhan...")

            # Print position details and profit target status
            for symbol, position in self.positions.items():
                current_price = self.current_prices.get(symbol, 0)
                if current_price > 0 and position.avg_price > 0:
                    pnl_pct = (current_price - position.avg_price) / position.avg_price
                    profit_cents = (current_price - position.avg_price) * 100 if position.side == 'long' else (position.avg_price - current_price) * 100
                    profit_target_status = f"✅ Profit target reached ({profit_cents:.2f}/{self.profit_target_cents:.2f} paise)" if profit_cents >= self.profit_target_cents else f"⏳ Profit target: {profit_cents:.2f}/{self.profit_target_cents:.2f} paise"
                    print(f"   {symbol}: Qty:{position.qty} @ ₹{position.avg_price:.2f} | Current: ₹{current_price:.2f} | {profit_target_status}")

            self.sync_positions_with_dhan()
            # Reschedule if still connected
            if self.is_connected:
                self.position_sync_timer = threading.Timer(self.position_sync_interval, sync_and_reschedule)
                self.position_sync_timer.daemon = True
                self.position_sync_timer.start()

        # Start the initial timer
        self.position_sync_timer = threading.Timer(self.position_sync_interval, sync_and_reschedule)
        self.position_sync_timer.daemon = True
        self.position_sync_timer.start()
        print(f"📊 Position sync timer started (every {self.position_sync_interval} seconds)")

    def start_trading(self):
        """Start the trading bot"""
        print("\n🚀 Starting Dhan Trading Bot for Indian Markets...")
        print(f"📊 Trading Configuration:")
        print(f"   Symbols: {', '.join(self.symbols)}")
        print(f"   Profit Target: {self.profit_target_cents:.2f} paise")
        print(f"   Initial Quantity: {self.initial_qty}")
        print(f"   Max Averaging Levels: {self.max_averaging_levels}")
        print(f"   Averaging Down Threshold: {self.averaging_down_threshold:.2%}")
        print(f"   Using {'Sandbox' if self.use_sandbox else 'Live'} Trading")

        if self.enable_reentry:
            print("\n🔄 Re-entry Strategy Enabled:")
            print(f"   - After taking profit, positions will be monitored for {self.reentry_monitoring_time} seconds")
            print(f"   - Re-entry will trigger if price drops by {abs(self.reentry_price_drop_pct)*100:.1f}% from exit price")

        # Check if market is open
        if not self.is_market_open():
            print("\n⚠️ MARKET IS CURRENTLY CLOSED")
            print(f"   Market hours: 9:15 AM - 3:30 PM IST (Monday-Friday)")
            now = datetime.now(IST)
            print(f"   Current time: {now.strftime('%I:%M %p')} IST on {now.strftime('%A, %d %B %Y')}")

            # Calculate time until market open
            if now.weekday() >= 5:  # Weekend
                days_until_monday = (7 - now.weekday()) % 7
                next_open = now.replace(hour=MARKET_OPEN_TIME.hour, minute=MARKET_OPEN_TIME.minute, second=0)
                next_open += timedelta(days=days_until_monday)
                time_until_open = next_open - now
                print(f"   Market opens in: {time_until_open.days} days, {time_until_open.seconds // 3600} hours")
            else:  # Weekday but market closed
                if now.time() < MARKET_OPEN_TIME:  # Before market open
                    next_open = now.replace(hour=MARKET_OPEN_TIME.hour, minute=MARKET_OPEN_TIME.minute, second=0)
                    time_until_open = next_open - now
                    print(f"   Market opens in: {time_until_open.seconds // 3600} hours, {(time_until_open.seconds % 3600) // 60} minutes")
                else:  # After market close
                    days_until_next = 1 if now.weekday() < 4 else (7 - now.weekday() + 0) % 7
                    next_open = now.replace(hour=MARKET_OPEN_TIME.hour, minute=MARKET_OPEN_TIME.minute, second=0)
                    next_open += timedelta(days=days_until_next)
                    time_until_open = next_open - now
                    print(f"   Market opens in: {time_until_open.days} days, {time_until_open.seconds // 3600} hours, {(time_until_open.seconds % 3600) // 60} minutes")

            print("\n⚠️ Bot will start, but no trades will be executed until market opens")

        # Get account information
        self.get_account_info()

        # Get current positions
        self.get_positions()

        # Start keyboard listener for emergency controls
        self.start_keyboard_listener()

        # Start position sync timer
        self.start_position_sync_timer()

        # Connect to WebSocket for real-time data
        print("\n🔌 Connecting to Dhan WebSocket...")

        # Initialize WebSocket
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_open=lambda ws: self.on_open(ws),
            on_message=lambda ws, msg: self.on_message(ws, msg),
            on_error=lambda ws, err: self.on_error(ws, err),
            on_close=lambda ws, close_status_code, close_msg: self.on_close(ws, close_status_code, close_msg)
        )

        # Start WebSocket in a separate thread
        self.ws_thread = threading.Thread(target=self.ws.run_forever, daemon=True)
        self.ws_thread.start()

        # Wait for WebSocket connection
        max_wait = 10
        waited = 0
        while waited < max_wait and not (self.is_connected and self.authenticated):
            time_module.sleep(0.5)
            waited += 0.5

        # If WebSocket connection fails, fall back to polling mode
        if not self.is_connected or not self.authenticated:
            print("⚠️ WebSocket connection not established. Falling back to polling mode.")
            print("   This means less frequent price updates but the bot will still function.")

            # Start a timer to poll for price updates
            self.start_price_polling()
        else:
            print("\n✅ WebSocket connected successfully!")

        print("\n✅ Trading bot started successfully!")
        print("Press 'q' for emergency close, 'p' for position summary, 's' to stop trading, 'a' to add to position")
        print("Press Ctrl+C to stop the bot gracefully")

        return self.ws_thread

    def start_price_polling(self):
        """Start polling for price updates as a fallback if WebSocket fails"""
        def poll_prices():
            while self.is_connected:
                try:
                    # Poll for each tracked symbol
                    for symbol in self.symbols:
                        exchange, trading_symbol = self.get_exchange_and_symbol(symbol)

                        # Use Dhan quote API to get current price
                        quote_url = f"{self.api_base}/quotes/{exchange}/{trading_symbol}"
                        response = requests.get(quote_url, headers=self.headers, timeout=5)

                        if response.status_code == 200:
                            quote_data = response.json()

                            # Extract price from response
                            if 'data' in quote_data and 'lastPrice' in quote_data['data']:
                                price = float(quote_data['data']['lastPrice'])

                                # Update current price
                                self.current_prices[symbol] = price

                                # Store price history (limited to last 100 prices)
                                self.price_history[symbol].append(price)
                                if len(self.price_history[symbol]) > 100:
                                    self.price_history[symbol].pop(0)

                                # Display real-time price updates and PnL calculations
                                if symbol in self.positions:
                                    position = self.positions[symbol]
                                    # Update position's market value and unrealized P&L
                                    if position.side == 'long':
                                        position.market_value = position.qty * price
                                        position.unrealized_pl = position.market_value - (position.qty * position.avg_price)
                                        pnl_pct = (price - position.avg_price) / position.avg_price * 100
                                    else:  # short position
                                        position.market_value = position.qty * price
                                        position.unrealized_pl = (position.qty * position.avg_price) - position.market_value
                                        pnl_pct = (position.avg_price - price) / position.avg_price * 100

                                    # Display price update with PnL information
                                    print(f"💹 {symbol}: ₹{price:.4f} (₹{position.unrealized_pl:.2f} | {pnl_pct:.2f}%)", end="\r")
                                else:
                                    # Just display price update if no position
                                    print(f"💹 {symbol}: ₹{price:.4f}", end="\r")

                                # Execute trading strategy
                                self.execute_trading_strategy(symbol, price)

                    # Sleep between polling cycles
                    time_module.sleep(5)  # Poll every 5 seconds

                except Exception as e:
                    logger.error(f"Error in price polling: {e}")
                    time_module.sleep(10)  # Wait longer after an error

        # Start polling in a separate thread
        polling_thread = threading.Thread(target=poll_prices, daemon=True)
        polling_thread.start()
        print("📊 Price polling started (every 5 seconds)")

    def stop_trading(self):
        """Stop trading and clean up resources"""
        print("\n🛑 Stopping trading...")

        # Stop keyboard listener
        self.stop_keyboard_listener()

        # Stop position sync timer
        if self.position_sync_timer:
            self.position_sync_timer.cancel()
            self.position_sync_timer = None
            print("📊 Position sync timer stopped")

        # Close WebSocket connection
        if self.ws:
            self.ws.close()

        # Print performance summary
        self.print_performance_summary()

        # Set flag to indicate bot is stopped
        self.is_connected = False
        self.authenticated = False

    def print_performance_summary(self):
        """Print a summary of trading performance"""
        print("\n" + "=" * 60)
        print("📈 TRADING PERFORMANCE SUMMARY")
        print("=" * 60)
        print(f"Total Trades: {self.performance.total_trades}")
        print(f"Successful Trades: {self.performance.successful_trades}")

        if self.performance.successful_trades > 0:
            success_rate = self.performance.successful_trades / self.performance.total_trades * 100
            print(f"Success Rate: {success_rate:.1f}%")
            print(f"Average Execution Time: {self.performance.avg_execution_time_ms:.2f}ms")
            print(f"Min Execution Time: {self.performance.min_execution_time_ms:.2f}ms")
            print(f"Max Execution Time: {self.performance.max_execution_time_ms:.2f}ms")
            print(f"Average Slippage: ₹{self.performance.avg_slippage:.4f}")
            print(f"Total Slippage: ₹{self.performance.total_slippage:.4f}")

        print("=" * 60)

def main():
    """Main function to run the Dhan trading bot"""
    # Load credentials from environment variables
    DHAN_API_KEY = os.getenv('DHAN_SANDBOX_CLIENT_SECRET')
    DHAN_CLIENT_ID = os.getenv('DHAN_SANDBOX_CLIENT_ID')
    DHAN_CLIENT_SECRET = os.getenv('DHAN_SANDBOX_CLIENT_SECRET')

    if not DHAN_API_KEY or not DHAN_CLIENT_ID:
        print("❌ Please set your Dhan API credentials in environment variables:")
        print("DHAN_SANDBOX_CLIENT_SECRET, DHAN_SANDBOX_CLIENT_ID")
        return

    # Trading configuration
    symbols = ["NSE:RELIANCE", "NSE:INFY", "NSE:TCS"]  # Symbols to trade (Indian stocks)
    profit_target_cents = 50  # 50 paise profit target
    use_sandbox = True  # Use sandbox environment

    # Averaging down configuration
    initial_qty = 10
    max_averaging_levels = 4
    averaging_down_threshold = -0.01  # 1% loss threshold

    # Re-entry strategy configuration
    enable_reentry = True  # Enable re-entry strategy
    reentry_price_drop_pct = -0.005  # Re-enter if price drops by 0.5% from exit price
    reentry_monitoring_time = 300  # Monitor for 5 minutes (300 seconds) after closing position

    # Create and start trading bot
    bot = DhanTradingBot(
        api_key=DHAN_API_KEY,
        client_id=DHAN_CLIENT_ID,
        client_secret=DHAN_CLIENT_SECRET,
        symbols=symbols,
        use_sandbox=use_sandbox,
        profit_target_cents=profit_target_cents,
        initial_qty=initial_qty,
        max_averaging_levels=max_averaging_levels,
        averaging_down_threshold=averaging_down_threshold,
        enable_reentry=enable_reentry,
        reentry_price_drop_pct=reentry_price_drop_pct,
        reentry_monitoring_time=reentry_monitoring_time
    )

    try:
        ws_thread = bot.start_trading()
        if ws_thread:
            # Instead of just joining the thread, we'll use a loop that can be interrupted
            running = True
            while running:
                try:
                    # Check if thread is still alive every second
                    ws_thread.join(1.0)
                    if not ws_thread.is_alive():
                        running = False
                except KeyboardInterrupt:
                    print("\n⚠️ Keyboard interrupt detected. Stopping bot gracefully...")
                    bot.stop_trading()
                    running = False
    except Exception as e:
        print(f"❌ Error in main thread: {e}")
    finally:
        # Make sure we always stop the bot properly
        if hasattr(bot, 'is_connected') and bot.is_connected:
            bot.stop_trading()
        print("👋 Exiting trading bot")

if __name__ == "__main__":
    main()

