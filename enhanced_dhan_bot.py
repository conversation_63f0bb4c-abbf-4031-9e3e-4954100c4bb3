import asyncio
import json
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict, deque
import websocket
import threading
import time
import requests
from typing import List, Dict, Optional, Tuple
import logging
import os
from dotenv import load_dotenv
import numpy as np
from dataclasses import dataclass, field
from enum import Enum

# Windows-compatible imports for emergency close
try:
    import msvcrt  # Windows
    WINDOWS = True
except ImportError:
    WINDOWS = False

load_dotenv()

API_KEY = os.getenv('DHAN_API_KEY')
SECRET_KEY = os.getenv('DHAN_API_SECRET')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

@dataclass
class TradeExecution:
    symbol: str
    side: str
    qty: int
    order_time: datetime
    fill_time: Optional[datetime] = None
    expected_price: float = 0.0
    actual_price: float = 0.0
    slippage: float = 0.0
    execution_time_ms: float = 0.0
    order_id: str = ""
    status: str = "submitted"

@dataclass
class Position:
    symbol: str
    qty: int
    side: str
    avg_price: float
    market_value: float
    unrealized_pl: float
    entry_time: datetime
    
@dataclass
class ClosedPosition:
    symbol: str
    exit_price: float
    exit_time: datetime
    profit_cents: float
    monitoring_active: bool = True
    price_history_after_close: List[float] = field(default_factory=list)
    
@dataclass
class PerformanceMetrics:
    total_trades: int = 0
    successful_trades: int = 0
    avg_execution_time_ms: float = 0.0
    avg_slippage: float = 0.0
    total_slippage: float = 0.0
    max_execution_time_ms: float = 0.0
    min_execution_time_ms: float = float('inf')
    executions: List[TradeExecution] = field(default_factory=list)

class EnhancedDhanBot:
    def __init__(self, api_key: str, secret_key: str, symbols: List[str], 
                 use_paper: bool = True, profit_target_cents: float = 15.0,
                 initial_qty: int = 25, max_averaging_levels: int = 5,
                 averaging_down_threshold: float = -0.02,
                 enable_reentry: bool = True,
                 reentry_price_drop_pct: float = -0.01,  # 1% drop for re-entry
                 reentry_monitoring_time: int = 300):  # 5 minutes (300 seconds)
        self.api_key = api_key
        self.secret_key = secret_key
        self.symbols = symbols if isinstance(symbols, list) else [symbols]
        self.use_paper = use_paper
        self.profit_target_cents = profit_target_cents
        
        # Averaging down parameters
        self.initial_qty = initial_qty
        self.max_averaging_levels = max_averaging_levels
        self.averaging_down_threshold = averaging_down_threshold
        self.current_averaging_level = 0
        
        # Peak price tracking parameters
        self.peak_tracking_window = 60  # Track peak over last 60 prices
        self.min_peak_profit = 0.05  # Minimum 0.05% profit to consider selling at peak
        self.recent_peaks = {}  # Symbol -> recent peak price
        self.peak_confirmation_counts = {}  # Symbol -> confirmation count
        self.required_peak_confirmations = 3  # Number of confirmations needed to confirm a peak
        
        # Re-entry strategy parameters
        self.enable_reentry = enable_reentry
        self.reentry_price_drop_pct = reentry_price_drop_pct
        self.reentry_monitoring_time = reentry_monitoring_time
        self.closed_positions = {}  # Symbol -> ClosedPosition
        self.reentry_timers = {}  # Symbol -> Timer
        
        # API endpoints
        self.api_base = "https://sandbox.dhan.co/v2"
        self.headers = {
            'DHAN-API-KEY-ID': self.api_key,
            'DHAN-API-SECRET-KEY': self.secret_key,
            'Content-Type': 'application/json'
        }
        
        # Trading state
        self.positions = {}
        self.pending_orders = {}
        self.performance = PerformanceMetrics()
        self.current_prices = {}
        self.account_info = None
        self.price_history = defaultdict(list)
        
        # WebSocket for real-time data
        self.ws = None
        self.is_connected = False
        self.authenticated = False
        
        # WebSocket URL
        self.ws_url = "wss://stream.sandbox.dhan.co/v2"
        
        # Position sync timer
        self.position_sync_timer = None
        self.position_sync_interval = 30  # seconds
        
        # Emergency close features
        self.emergency_close_enabled = True
        self.keyboard_listener_active = False
        self.emergency_close_in_progress = False
        
    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            response = requests.get(f'{self.api_base}/account', headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                self.account_info = response.json()
                
                print("=" * 60)
                print("📋 DHAN ACCOUNT INFORMATION")
                print("=" * 60)
                print(f"Account Number: {self.account_info.get('account_number', 'N/A')}")
                print(f"Status: {self.account_info.get('status', 'N/A')}")
                print(f"Currency: {self.account_info.get('currency', 'N/A')}")
                print(f"Buying Power: ${float(self.account_info.get('buying_power', 0)):,.2f}")
                print(f"Cash: ${float(self.account_info.get('cash', 0)):,.2f}")
                print(f"Portfolio Value: ${float(self.account_info.get('portfolio_value', 0)):,.2f}")
                print(f"Day Trading Buying Power: ${float(self.account_info.get('daytrading_buying_power', 0)):,.2f}")
                print(f"Day Trade Count: {self.account_info.get('daytrade_count', 0)}")
                print(f"Pattern Day Trader: {self.account_info.get('pattern_day_trader', False)}")
                print(f"Trading Blocked: {self.account_info.get('trading_blocked', False)}")
                print(f"Account Blocked: {self.account_info.get('account_blocked', False)}")
                print("=" * 60)
                
                return self.account_info
            else:
                logger.error(f"Failed to get account info: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_positions(self) -> Dict[str, Position]:
        """Get current positions"""
        try:
            response = requests.get(f'{self.api_base}/positions', headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                positions_data = response.json()
                self.positions = {}
                
                for pos_data in positions_data:
                    symbol = pos_data['symbol']
                    
                    # Handle different field names for average cost
                    avg_price = float(pos_data.get('avg_entry_price', 0.0))
                    qty = int(pos_data.get('qty', 0))
                    side = pos_data.get('side', 'buy')
                    market_value = float(pos_data.get('market_value', 0.0))
                    unrealized_pl = float(pos_data.get('unrealized_pl', 0.0))
                    entry_time = datetime.strptime(pos_data.get('entry_time', datetime.now().isoformat()), '%Y-%m-%dT%H:%M:%S.%fZ')
                    
                    position = Position(symbol=symbol, qty=qty, side=side, avg_price=avg_price, market_value=market_value, unrealized_pl=unrealized_pl, entry_time=entry_time)
                    self.positions[symbol] = position
                
                return self.positions
            else:
                logger.error(f"Failed to get positions: {response.status_code} - {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {}

    # Additional methods for trading logic will be implemented here